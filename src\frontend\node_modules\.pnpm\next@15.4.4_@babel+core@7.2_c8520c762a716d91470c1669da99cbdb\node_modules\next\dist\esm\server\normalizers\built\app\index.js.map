{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/index.ts"], "sourcesContent": ["import {\n  AppBundlePath<PERSON>ormalizer,\n  DevAppBundlePathNormalizer,\n} from './app-bundle-path-normalizer'\nimport { AppFilenameNormalizer } from './app-filename-normalizer'\nimport { DevAppPageNormalizer } from './app-page-normalizer'\nimport {\n  App<PERSON><PERSON><PERSON><PERSON>ormali<PERSON>,\n  DevAppPathnameNormalizer,\n} from './app-pathname-normalizer'\n\nexport class AppNormalizers {\n  public readonly filename: AppFilenameNormalizer\n  public readonly pathname: AppPathnameNormalizer\n  public readonly bundlePath: AppBundlePathNormalizer\n\n  constructor(distDir: string) {\n    this.filename = new AppFilenameNormalizer(distDir)\n    this.pathname = new AppPathnameNormalizer()\n    this.bundlePath = new AppBundlePathNormalizer()\n  }\n}\n\nexport class DevAppNormalizers {\n  public readonly page: DevAppPageNormalizer\n  public readonly pathname: DevAppPathnameNormalizer\n  public readonly bundlePath: DevAppBundlePathNormalizer\n\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    isTurbopack: boolean\n  ) {\n    this.page = new DevAppPageNormalizer(appDir, extensions, isTurbopack)\n    this.pathname = new DevAppPathnameNormalizer(this.page)\n    this.bundlePath = new DevAppBundlePathNormalizer(this.page, isTurbopack)\n  }\n}\n"], "names": ["AppBundlePathNormalizer", "DevAppBundlePathNormalizer", "AppFilenameNormalizer", "DevAppPageNormalizer", "AppPathnameNormalizer", "DevAppPathnameNormalizer", "AppNormalizers", "constructor", "distDir", "filename", "pathname", "bundlePath", "DevAppNormalizers", "appDir", "extensions", "isTurbopack", "page"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,0BAA0B,QACrB,+BAA8B;AACrC,SAASC,qBAAqB,QAAQ,4BAA2B;AACjE,SAASC,oBAAoB,QAAQ,wBAAuB;AAC5D,SACEC,qBAAqB,EACrBC,wBAAwB,QACnB,4BAA2B;AAElC,OAAO,MAAMC;IAKXC,YAAYC,OAAe,CAAE;QAC3B,IAAI,CAACC,QAAQ,GAAG,IAAIP,sBAAsBM;QAC1C,IAAI,CAACE,QAAQ,GAAG,IAAIN;QACpB,IAAI,CAACO,UAAU,GAAG,IAAIX;IACxB;AACF;AAEA,OAAO,MAAMY;IAKXL,YACEM,MAAc,EACdC,UAAiC,EACjCC,WAAoB,CACpB;QACA,IAAI,CAACC,IAAI,GAAG,IAAIb,qBAAqBU,QAAQC,YAAYC;QACzD,IAAI,CAACL,QAAQ,GAAG,IAAIL,yBAAyB,IAAI,CAACW,IAAI;QACtD,IAAI,CAACL,UAAU,GAAG,IAAIV,2BAA2B,IAAI,CAACe,IAAI,EAAED;IAC9D;AACF", "ignoreList": [0]}