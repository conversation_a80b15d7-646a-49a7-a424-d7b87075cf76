{"version": 3, "sources": ["../../../../../../src/server/route-modules/pages/vendored/contexts/router-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].RouterContext\n"], "names": ["module", "exports", "require", "vendored", "RouterContext"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,WAAW,CAACC,aAAa", "ignoreList": [0]}