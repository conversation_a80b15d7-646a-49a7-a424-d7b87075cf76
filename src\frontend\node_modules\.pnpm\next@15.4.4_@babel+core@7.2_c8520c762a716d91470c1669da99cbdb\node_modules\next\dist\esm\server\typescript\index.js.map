{"version": 3, "sources": ["../../../src/server/typescript/index.ts"], "sourcesContent": ["/**\n * This is a TypeScript language service plugin for Next.js app directory,\n * it provides the following features:\n *\n * - Warns about disallowed React APIs in server components.\n * - Warns about disallowed layout and page exports.\n * - Autocompletion for entry configurations.\n * - Hover hint and docs for entry configurations.\n */\n\nimport {\n  init,\n  getEntryInfo,\n  isAppEntryFile,\n  isDefaultFunctionExport,\n  isPositionInsideNode,\n  getSource,\n  isInsideApp,\n} from './utils'\nimport { NEXT_TS_ERRORS } from './constant'\n\nimport entryConfig from './rules/config'\nimport serverLayer from './rules/server'\nimport entryDefault from './rules/entry'\nimport clientBoundary from './rules/client-boundary'\nimport serverBoundary from './rules/server-boundary'\nimport metadata from './rules/metadata'\nimport errorEntry from './rules/error'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nexport const createTSPlugin: tsModule.server.PluginModuleFactory = ({\n  typescript: ts,\n}) => {\n  function create(info: tsModule.server.PluginCreateInfo) {\n    // Get plugin options\n    // config is the plugin options from the user's tsconfig.json\n    // e.g. { \"plugins\": [{ \"name\": \"next\", \"enabled\": true }] }\n    // config will be { \"name\": \"next\", \"enabled\": true }\n    // The default user config is { \"name\": \"next\" }\n    const isPluginEnabled = info.config.enabled ?? true\n\n    if (!isPluginEnabled) {\n      return info.languageService\n    }\n\n    init({\n      ts,\n      info,\n    })\n\n    // Set up decorator object\n    const proxy: tsModule.LanguageService = Object.create(null)\n    for (let k of Object.keys(info.languageService)) {\n      const x = info.languageService[k as keyof tsModule.LanguageService]\n      // @ts-expect-error - JS runtime trickery which is tricky to type tersely\n      proxy[k] = (...args: Array<{}>) => x.apply(info.languageService, args)\n    }\n\n    // Auto completion\n    proxy.getCompletionsAtPosition = (\n      fileName: string,\n      position: number,\n      options: any\n    ) => {\n      let prior = info.languageService.getCompletionsAtPosition(\n        fileName,\n        position,\n        options\n      ) || {\n        isGlobalCompletion: false,\n        isMemberCompletion: false,\n        isNewIdentifierLocation: false,\n        entries: [],\n      }\n      if (!isAppEntryFile(fileName)) return prior\n\n      // If it's a server entry.\n      const entryInfo = getEntryInfo(fileName)\n      if (!entryInfo.client) {\n        // Remove specified entries from completion list\n        prior.entries = serverLayer.filterCompletionsAtPosition(prior.entries)\n      }\n\n      // Add auto completions for export configs.\n      entryConfig.addCompletionsAtPosition(fileName, position, prior)\n\n      const source = getSource(fileName)\n      if (!source) return prior\n\n      ts.forEachChild(source!, (node) => {\n        // Auto completion for default export function's props.\n        if (\n          isPositionInsideNode(position, node) &&\n          isDefaultFunctionExport(node)\n        ) {\n          prior.entries.push(\n            ...entryDefault.getCompletionsAtPosition(\n              fileName,\n              node as tsModule.FunctionDeclaration,\n              position\n            )\n          )\n        }\n      })\n\n      return prior\n    }\n\n    // Show auto completion details\n    proxy.getCompletionEntryDetails = (\n      fileName: string,\n      position: number,\n      entryName: string,\n      formatOptions: tsModule.FormatCodeOptions,\n      source: string,\n      preferences: tsModule.UserPreferences,\n      data: tsModule.CompletionEntryData\n    ) => {\n      const entryCompletionEntryDetails = entryConfig.getCompletionEntryDetails(\n        entryName,\n        data,\n        fileName\n      )\n      if (entryCompletionEntryDetails) return entryCompletionEntryDetails\n\n      return info.languageService.getCompletionEntryDetails(\n        fileName,\n        position,\n        entryName,\n        formatOptions,\n        source,\n        preferences,\n        data\n      )\n    }\n\n    // Quick info\n    proxy.getQuickInfoAtPosition = (fileName: string, position: number) => {\n      const prior = info.languageService.getQuickInfoAtPosition(\n        fileName,\n        position\n      )\n      if (!isAppEntryFile(fileName)) return prior\n\n      // Remove type suggestions for disallowed APIs in server components.\n      const entryInfo = getEntryInfo(fileName)\n      if (!entryInfo.client) {\n        const definitions = info.languageService.getDefinitionAtPosition(\n          fileName,\n          position\n        )\n        if (\n          definitions &&\n          serverLayer.hasDisallowedReactAPIDefinition(definitions)\n        ) {\n          return\n        }\n      }\n\n      const overridden = entryConfig.getQuickInfoAtPosition(fileName, position)\n      if (overridden) return overridden\n\n      return prior\n    }\n\n    // Show errors for disallowed imports\n    proxy.getSemanticDiagnostics = (fileName: string) => {\n      const prior = info.languageService.getSemanticDiagnostics(fileName)\n      const source = getSource(fileName)\n      if (!source) return prior\n\n      let isClientEntry = false\n      let isServerEntry = false\n      const isAppEntry = isAppEntryFile(fileName)\n\n      try {\n        const entryInfo = getEntryInfo(fileName, true)\n        isClientEntry = entryInfo.client\n        isServerEntry = entryInfo.server\n      } catch (e: any) {\n        prior.push({\n          file: source,\n          category: ts.DiagnosticCategory.Error,\n          code: NEXT_TS_ERRORS.MISPLACED_ENTRY_DIRECTIVE,\n          ...e,\n        })\n        isClientEntry = false\n        isServerEntry = false\n      }\n\n      if (isInsideApp(fileName)) {\n        const errorDiagnostic = errorEntry.getSemanticDiagnostics(\n          source!,\n          isClientEntry\n        )\n        prior.push(...errorDiagnostic)\n      }\n\n      ts.forEachChild(source!, (node) => {\n        if (ts.isImportDeclaration(node)) {\n          // import ...\n          if (isAppEntry) {\n            if (!isClientEntry || isServerEntry) {\n              // Check if it has valid imports in the server layer\n              const diagnostics =\n                serverLayer.getSemanticDiagnosticsForImportDeclaration(\n                  source,\n                  node\n                )\n              prior.push(...diagnostics)\n            }\n          }\n        } else if (\n          ts.isVariableStatement(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          // export const ...\n          if (isAppEntry) {\n            // Check if it has correct option exports\n            const diagnostics =\n              entryConfig.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            const metadataDiagnostics = isClientEntry\n              ? metadata.client.getSemanticDiagnosticsForExportVariableStatement(\n                  fileName,\n                  node\n                )\n              : metadata.server.getSemanticDiagnosticsForExportVariableStatement(\n                  fileName,\n                  node\n                )\n            prior.push(...diagnostics, ...metadataDiagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            )\n          }\n        } else if (isDefaultFunctionExport(node)) {\n          // export default function ...\n          if (isAppEntry) {\n            const diagnostics = entryDefault.getSemanticDiagnostics(\n              fileName,\n              source,\n              node\n            )\n            prior.push(...diagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n        } else if (\n          ts.isFunctionDeclaration(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          // export function ...\n          if (isAppEntry) {\n            const metadataDiagnostics = isClientEntry\n              ? metadata.client.getSemanticDiagnosticsForExportVariableStatement(\n                  fileName,\n                  node\n                )\n              : metadata.server.getSemanticDiagnosticsForExportVariableStatement(\n                  fileName,\n                  node\n                )\n            prior.push(...metadataDiagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n        } else if (ts.isExportDeclaration(node)) {\n          // export { ... }\n          if (isAppEntry) {\n            const metadataDiagnostics = isClientEntry\n              ? metadata.client.getSemanticDiagnosticsForExportDeclaration(\n                  fileName,\n                  node\n                )\n              : metadata.server.getSemanticDiagnosticsForExportDeclaration(\n                  fileName,\n                  node\n                )\n            prior.push(...metadataDiagnostics)\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForExportDeclaration(\n                source,\n                node\n              )\n            )\n          }\n        }\n      })\n\n      return prior\n    }\n\n    return proxy\n  }\n\n  return { create }\n}\n"], "names": ["init", "getEntryInfo", "isAppEntryFile", "isDefaultFunctionExport", "isPositionInsideNode", "getSource", "isInsideApp", "NEXT_TS_ERRORS", "entryConfig", "serverLayer", "<PERSON><PERSON><PERSON><PERSON>", "clientBoundary", "serverBoundary", "metadata", "errorEntry", "createTSPlugin", "typescript", "ts", "create", "info", "isPluginEnabled", "config", "enabled", "languageService", "proxy", "Object", "k", "keys", "x", "args", "apply", "getCompletionsAtPosition", "fileName", "position", "options", "prior", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "entryInfo", "client", "filterCompletionsAtPosition", "addCompletionsAtPosition", "source", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "push", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "data", "entryCompletionEntryDetails", "getQuickInfoAtPosition", "definitions", "getDefinitionAtPosition", "hasDisallowedReactAPIDefinition", "overridden", "getSemanticDiagnostics", "isClientEntry", "isServerEntry", "isAppEntry", "server", "e", "file", "category", "DiagnosticCategory", "Error", "code", "MISPLACED_ENTRY_DIRECTIVE", "errorDiagnostic", "isImportDeclaration", "diagnostics", "getSemanticDiagnosticsForImportDeclaration", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "getSemanticDiagnosticsForExportVariableStatement", "metadataDiagnostics", "getSemanticDiagnosticsForFunctionExport", "isFunctionDeclaration", "isExportDeclaration", "getSemanticDiagnosticsForExportDeclaration"], "mappings": "AAAA;;;;;;;;CAQC,GAED,SACEA,IAAI,EACJC,YAAY,EACZC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,SAAS,EACTC,WAAW,QACN,UAAS;AAChB,SAASC,cAAc,QAAQ,aAAY;AAE3C,OAAOC,iBAAiB,iBAAgB;AACxC,OAAOC,iBAAiB,iBAAgB;AACxC,OAAOC,kBAAkB,gBAAe;AACxC,OAAOC,oBAAoB,0BAAyB;AACpD,OAAOC,oBAAoB,0BAAyB;AACpD,OAAOC,cAAc,mBAAkB;AACvC,OAAOC,gBAAgB,gBAAe;AAGtC,OAAO,MAAMC,iBAAsD,CAAC,EAClEC,YAAYC,EAAE,EACf;IACC,SAASC,OAAOC,IAAsC;QACpD,qBAAqB;QACrB,6DAA6D;QAC7D,4DAA4D;QAC5D,qDAAqD;QACrD,gDAAgD;QAChD,MAAMC,kBAAkBD,KAAKE,MAAM,CAACC,OAAO,IAAI;QAE/C,IAAI,CAACF,iBAAiB;YACpB,OAAOD,KAAKI,eAAe;QAC7B;QAEAvB,KAAK;YACHiB;YACAE;QACF;QAEA,0BAA0B;QAC1B,MAAMK,QAAkCC,OAAOP,MAAM,CAAC;QACtD,KAAK,IAAIQ,KAAKD,OAAOE,IAAI,CAACR,KAAKI,eAAe,EAAG;YAC/C,MAAMK,IAAIT,KAAKI,eAAe,CAACG,EAAoC;YACnE,yEAAyE;YACzEF,KAAK,CAACE,EAAE,GAAG,CAAC,GAAGG,OAAoBD,EAAEE,KAAK,CAACX,KAAKI,eAAe,EAAEM;QACnE;QAEA,kBAAkB;QAClBL,MAAMO,wBAAwB,GAAG,CAC/BC,UACAC,UACAC;YAEA,IAAIC,QAAQhB,KAAKI,eAAe,CAACQ,wBAAwB,CACvDC,UACAC,UACAC,YACG;gBACHE,oBAAoB;gBACpBC,oBAAoB;gBACpBC,yBAAyB;gBACzBC,SAAS,EAAE;YACb;YACA,IAAI,CAACrC,eAAe8B,WAAW,OAAOG;YAEtC,0BAA0B;YAC1B,MAAMK,YAAYvC,aAAa+B;YAC/B,IAAI,CAACQ,UAAUC,MAAM,EAAE;gBACrB,gDAAgD;gBAChDN,MAAMI,OAAO,GAAG9B,YAAYiC,2BAA2B,CAACP,MAAMI,OAAO;YACvE;YAEA,2CAA2C;YAC3C/B,YAAYmC,wBAAwB,CAACX,UAAUC,UAAUE;YAEzD,MAAMS,SAASvC,UAAU2B;YACzB,IAAI,CAACY,QAAQ,OAAOT;YAEpBlB,GAAG4B,YAAY,CAACD,QAAS,CAACE;gBACxB,uDAAuD;gBACvD,IACE1C,qBAAqB6B,UAAUa,SAC/B3C,wBAAwB2C,OACxB;oBACAX,MAAMI,OAAO,CAACQ,IAAI,IACbrC,aAAaqB,wBAAwB,CACtCC,UACAc,MACAb;gBAGN;YACF;YAEA,OAAOE;QACT;QAEA,+BAA+B;QAC/BX,MAAMwB,yBAAyB,GAAG,CAChChB,UACAC,UACAgB,WACAC,eACAN,QACAO,aACAC;YAEA,MAAMC,8BAA8B7C,YAAYwC,yBAAyB,CACvEC,WACAG,MACApB;YAEF,IAAIqB,6BAA6B,OAAOA;YAExC,OAAOlC,KAAKI,eAAe,CAACyB,yBAAyB,CACnDhB,UACAC,UACAgB,WACAC,eACAN,QACAO,aACAC;QAEJ;QAEA,aAAa;QACb5B,MAAM8B,sBAAsB,GAAG,CAACtB,UAAkBC;YAChD,MAAME,QAAQhB,KAAKI,eAAe,CAAC+B,sBAAsB,CACvDtB,UACAC;YAEF,IAAI,CAAC/B,eAAe8B,WAAW,OAAOG;YAEtC,oEAAoE;YACpE,MAAMK,YAAYvC,aAAa+B;YAC/B,IAAI,CAACQ,UAAUC,MAAM,EAAE;gBACrB,MAAMc,cAAcpC,KAAKI,eAAe,CAACiC,uBAAuB,CAC9DxB,UACAC;gBAEF,IACEsB,eACA9C,YAAYgD,+BAA+B,CAACF,cAC5C;oBACA;gBACF;YACF;YAEA,MAAMG,aAAalD,YAAY8C,sBAAsB,CAACtB,UAAUC;YAChE,IAAIyB,YAAY,OAAOA;YAEvB,OAAOvB;QACT;QAEA,qCAAqC;QACrCX,MAAMmC,sBAAsB,GAAG,CAAC3B;YAC9B,MAAMG,QAAQhB,KAAKI,eAAe,CAACoC,sBAAsB,CAAC3B;YAC1D,MAAMY,SAASvC,UAAU2B;YACzB,IAAI,CAACY,QAAQ,OAAOT;YAEpB,IAAIyB,gBAAgB;YACpB,IAAIC,gBAAgB;YACpB,MAAMC,aAAa5D,eAAe8B;YAElC,IAAI;gBACF,MAAMQ,YAAYvC,aAAa+B,UAAU;gBACzC4B,gBAAgBpB,UAAUC,MAAM;gBAChCoB,gBAAgBrB,UAAUuB,MAAM;YAClC,EAAE,OAAOC,GAAQ;gBACf7B,MAAMY,IAAI,CAAC;oBACTkB,MAAMrB;oBACNsB,UAAUjD,GAAGkD,kBAAkB,CAACC,KAAK;oBACrCC,MAAM9D,eAAe+D,yBAAyB;oBAC9C,GAAGN,CAAC;gBACN;gBACAJ,gBAAgB;gBAChBC,gBAAgB;YAClB;YAEA,IAAIvD,YAAY0B,WAAW;gBACzB,MAAMuC,kBAAkBzD,WAAW6C,sBAAsB,CACvDf,QACAgB;gBAEFzB,MAAMY,IAAI,IAAIwB;YAChB;YAEAtD,GAAG4B,YAAY,CAACD,QAAS,CAACE;oBAgBtBA,iBAqEAA;gBApFF,IAAI7B,GAAGuD,mBAAmB,CAAC1B,OAAO;oBAChC,aAAa;oBACb,IAAIgB,YAAY;wBACd,IAAI,CAACF,iBAAiBC,eAAe;4BACnC,oDAAoD;4BACpD,MAAMY,cACJhE,YAAYiE,0CAA0C,CACpD9B,QACAE;4BAEJX,MAAMY,IAAI,IAAI0B;wBAChB;oBACF;gBACF,OAAO,IACLxD,GAAG0D,mBAAmB,CAAC7B,WACvBA,kBAAAA,KAAK8B,SAAS,qBAAd9B,gBAAgB+B,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK9D,GAAG+D,UAAU,CAACC,aAAa,IAClE;oBACA,mBAAmB;oBACnB,IAAInB,YAAY;wBACd,yCAAyC;wBACzC,MAAMW,cACJjE,YAAY0E,gDAAgD,CAC1DtC,QACAE;wBAEJ,MAAMqC,sBAAsBvB,gBACxB/C,SAAS4B,MAAM,CAACyC,gDAAgD,CAC9DlD,UACAc,QAEFjC,SAASkD,MAAM,CAACmB,gDAAgD,CAC9DlD,UACAc;wBAENX,MAAMY,IAAI,IAAI0B,gBAAgBU;oBAChC;oBAEA,IAAIvB,eAAe;wBACjBzB,MAAMY,IAAI,IACLpC,eAAeuE,gDAAgD,CAChEtC,QACAE;oBAGN;oBAEA,IAAIe,eAAe;wBACjB1B,MAAMY,IAAI,IACLnC,eAAesE,gDAAgD,CAChEtC,QACAE;oBAGN;gBACF,OAAO,IAAI3C,wBAAwB2C,OAAO;oBACxC,8BAA8B;oBAC9B,IAAIgB,YAAY;wBACd,MAAMW,cAAc/D,aAAaiD,sBAAsB,CACrD3B,UACAY,QACAE;wBAEFX,MAAMY,IAAI,IAAI0B;oBAChB;oBAEA,IAAIb,eAAe;wBACjBzB,MAAMY,IAAI,IACLpC,eAAeyE,uCAAuC,CACvDxC,QACAE;oBAGN;oBAEA,IAAIe,eAAe;wBACjB1B,MAAMY,IAAI,IACLnC,eAAewE,uCAAuC,CACvDxC,QACAE;oBAGN;gBACF,OAAO,IACL7B,GAAGoE,qBAAqB,CAACvC,WACzBA,mBAAAA,KAAK8B,SAAS,qBAAd9B,iBAAgB+B,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK9D,GAAG+D,UAAU,CAACC,aAAa,IAClE;oBACA,sBAAsB;oBACtB,IAAInB,YAAY;wBACd,MAAMqB,sBAAsBvB,gBACxB/C,SAAS4B,MAAM,CAACyC,gDAAgD,CAC9DlD,UACAc,QAEFjC,SAASkD,MAAM,CAACmB,gDAAgD,CAC9DlD,UACAc;wBAENX,MAAMY,IAAI,IAAIoC;oBAChB;oBAEA,IAAIvB,eAAe;wBACjBzB,MAAMY,IAAI,IACLpC,eAAeyE,uCAAuC,CACvDxC,QACAE;oBAGN;oBAEA,IAAIe,eAAe;wBACjB1B,MAAMY,IAAI,IACLnC,eAAewE,uCAAuC,CACvDxC,QACAE;oBAGN;gBACF,OAAO,IAAI7B,GAAGqE,mBAAmB,CAACxC,OAAO;oBACvC,iBAAiB;oBACjB,IAAIgB,YAAY;wBACd,MAAMqB,sBAAsBvB,gBACxB/C,SAAS4B,MAAM,CAAC8C,0CAA0C,CACxDvD,UACAc,QAEFjC,SAASkD,MAAM,CAACwB,0CAA0C,CACxDvD,UACAc;wBAENX,MAAMY,IAAI,IAAIoC;oBAChB;oBAEA,IAAItB,eAAe;wBACjB1B,MAAMY,IAAI,IACLnC,eAAe2E,0CAA0C,CAC1D3C,QACAE;oBAGN;gBACF;YACF;YAEA,OAAOX;QACT;QAEA,OAAOX;IACT;IAEA,OAAO;QAAEN;IAAO;AAClB,EAAC", "ignoreList": [0]}