{"version": 3, "sources": ["../../src/server/server-utils.ts"], "sourcesContent": ["import type { Rewrite } from '../lib/load-custom-routes'\nimport type { RouteMatchFn } from '../shared/lib/router/utils/route-matcher'\nimport type { NextConfig } from './config'\nimport type { BaseNextRequest } from './base-http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\n\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { getPathMatch } from '../shared/lib/router/utils/path-match'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport {\n  matchHas,\n  prepareDestination,\n} from '../shared/lib/router/utils/prepare-destination'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { normalizeRscURL } from '../shared/lib/router/utils/app-paths'\nimport {\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../lib/constants'\nimport { normalizeNextQueryParam } from './web/utils'\nimport type { IncomingHttpHeaders, IncomingMessage } from 'http'\nimport { decodeQueryPathParameter } from './lib/decode-query-path-parameter'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { parseReqUrl } from '../lib/url'\nimport { formatUrl } from '../shared/lib/router/utils/format-url'\nimport { parseAndValidateFlightRouterState } from './app-render/parse-and-validate-flight-router-state'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport { NEXT_ROUTER_STATE_TREE_HEADER } from '../client/components/app-router-headers'\nimport { getSelectedParams } from '../client/components/router-reducer/compute-changed-path'\n\nfunction filterInternalQuery(\n  query: Record<string, undefined | string | string[]>,\n  paramKeys: string[]\n) {\n  // this is used to pass query information in rewrites\n  // but should not be exposed in final query\n  delete query['nextInternalLocale']\n\n  for (const key in query) {\n    const isNextQueryPrefix =\n      key !== NEXT_QUERY_PARAM_PREFIX && key.startsWith(NEXT_QUERY_PARAM_PREFIX)\n\n    const isNextInterceptionMarkerPrefix =\n      key !== NEXT_INTERCEPTION_MARKER_PREFIX &&\n      key.startsWith(NEXT_INTERCEPTION_MARKER_PREFIX)\n\n    if (\n      isNextQueryPrefix ||\n      isNextInterceptionMarkerPrefix ||\n      paramKeys.includes(key)\n    ) {\n      delete query[key]\n    }\n  }\n}\n\nexport function normalizeCdnUrl(\n  req: BaseNextRequest | IncomingMessage,\n  paramKeys: string[]\n) {\n  // make sure to normalize req.url from CDNs to strip dynamic and rewrite\n  // params from the query which are added during routing\n  const _parsedUrl = parseReqUrl(req.url!)\n\n  // we can't normalize if we can't parse\n  if (!_parsedUrl) {\n    return req.url\n  }\n  delete (_parsedUrl as any).search\n  filterInternalQuery(_parsedUrl.query, paramKeys)\n\n  req.url = formatUrl(_parsedUrl)\n}\n\nexport function interpolateDynamicPath(\n  pathname: string,\n  params: ParsedUrlQuery,\n  defaultRouteRegex?: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  if (!defaultRouteRegex) return pathname\n\n  for (const param of Object.keys(defaultRouteRegex.groups)) {\n    const { optional, repeat } = defaultRouteRegex.groups[param]\n    let builtParam = `[${repeat ? '...' : ''}${param}]`\n\n    if (optional) {\n      builtParam = `[${builtParam}]`\n    }\n\n    let paramValue: string\n    const value = params[param]\n\n    if (Array.isArray(value)) {\n      paramValue = value.map((v) => v && encodeURIComponent(v)).join('/')\n    } else if (value) {\n      paramValue = encodeURIComponent(value)\n    } else {\n      paramValue = ''\n    }\n\n    if (paramValue || optional) {\n      pathname = pathname.replaceAll(builtParam, paramValue)\n    }\n  }\n\n  return pathname\n}\n\nexport function normalizeDynamicRouteParams(\n  query: ParsedUrlQuery,\n  defaultRouteRegex: ReturnType<typeof getNamedRouteRegex>,\n  defaultRouteMatches: ParsedUrlQuery,\n  ignoreMissingOptional: boolean\n) {\n  let hasValidParams = true\n  let params: ParsedUrlQuery = {}\n\n  for (const key of Object.keys(defaultRouteRegex.groups)) {\n    let value: string | string[] | undefined = query[key]\n\n    if (typeof value === 'string') {\n      value = normalizeRscURL(value)\n    } else if (Array.isArray(value)) {\n      value = value.map(normalizeRscURL)\n    }\n\n    // if the value matches the default value we can't rely\n    // on the parsed params, this is used to signal if we need\n    // to parse x-now-route-matches or not\n    const defaultValue = defaultRouteMatches![key]\n    const isOptional = defaultRouteRegex!.groups[key].optional\n\n    const isDefaultValue = Array.isArray(defaultValue)\n      ? defaultValue.some((defaultVal) => {\n          return Array.isArray(value)\n            ? value.some((val) => val.includes(defaultVal))\n            : value?.includes(defaultVal)\n        })\n      : value?.includes(defaultValue as string)\n\n    if (\n      isDefaultValue ||\n      (typeof value === 'undefined' && !(isOptional && ignoreMissingOptional))\n    ) {\n      return { params: {}, hasValidParams: false }\n    }\n\n    // non-provided optional values should be undefined so normalize\n    // them to undefined\n    if (\n      isOptional &&\n      (!value ||\n        (Array.isArray(value) &&\n          value.length === 1 &&\n          // fallback optional catch-all SSG pages have\n          // [[...paramName]] for the root path on Vercel\n          (value[0] === 'index' || value[0] === `[[...${key}]]`)))\n    ) {\n      value = undefined\n      delete query[key]\n    }\n\n    // query values from the proxy aren't already split into arrays\n    // so make sure to normalize catch-all values\n    if (\n      value &&\n      typeof value === 'string' &&\n      defaultRouteRegex!.groups[key].repeat\n    ) {\n      value = value.split('/')\n    }\n\n    if (value) {\n      params[key] = value\n    }\n  }\n\n  return {\n    params,\n    hasValidParams,\n  }\n}\n\nexport function getServerUtils({\n  page,\n  i18n,\n  basePath,\n  rewrites,\n  pageIsDynamic,\n  trailingSlash,\n  caseSensitive,\n}: {\n  page: string\n  i18n?: NextConfig['i18n']\n  basePath: string\n  rewrites: DeepReadonly<{\n    fallback?: ReadonlyArray<Rewrite>\n    afterFiles?: ReadonlyArray<Rewrite>\n    beforeFiles?: ReadonlyArray<Rewrite>\n  }>\n  pageIsDynamic: boolean\n  trailingSlash?: boolean\n  caseSensitive: boolean\n}) {\n  let defaultRouteRegex: ReturnType<typeof getNamedRouteRegex> | undefined\n  let dynamicRouteMatcher: RouteMatchFn | undefined\n  let defaultRouteMatches: ParsedUrlQuery | undefined\n\n  if (pageIsDynamic) {\n    defaultRouteRegex = getNamedRouteRegex(page, {\n      prefixRouteKeys: false,\n    })\n    dynamicRouteMatcher = getRouteMatcher(defaultRouteRegex)\n    defaultRouteMatches = dynamicRouteMatcher(page) as ParsedUrlQuery\n  }\n\n  function handleRewrites(\n    req: BaseNextRequest | IncomingMessage,\n    parsedUrl: UrlWithParsedQuery\n  ) {\n    const rewriteParams: Record<string, string> = {}\n    let fsPathname = parsedUrl.pathname\n\n    const matchesPage = () => {\n      const fsPathnameNoSlash = removeTrailingSlash(fsPathname || '')\n      return (\n        fsPathnameNoSlash === removeTrailingSlash(page) ||\n        dynamicRouteMatcher?.(fsPathnameNoSlash)\n      )\n    }\n\n    const checkRewrite = (rewrite: DeepReadonly<Rewrite>): boolean => {\n      const matcher = getPathMatch(\n        rewrite.source + (trailingSlash ? '(/)?' : ''),\n        {\n          removeUnnamedParams: true,\n          strict: true,\n          sensitive: !!caseSensitive,\n        }\n      )\n\n      if (!parsedUrl.pathname) return false\n\n      let params = matcher(parsedUrl.pathname)\n\n      if ((rewrite.has || rewrite.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          rewrite.has as Rewrite['has'],\n          rewrite.missing as Rewrite['missing']\n        )\n\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        try {\n          // An interception rewrite might reference a dynamic param for a route the user\n          // is currently on, which wouldn't be extractable from the matched route params.\n          // This attempts to extract the dynamic params from the provided router state.\n          if (isInterceptionRouteRewrite(rewrite as Rewrite)) {\n            const stateHeader =\n              req.headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n\n            if (stateHeader) {\n              params = {\n                ...getSelectedParams(\n                  parseAndValidateFlightRouterState(stateHeader)\n                ),\n                ...params,\n              }\n            }\n          }\n        } catch (err) {\n          // this is a no-op -- we couldn't extract dynamic params from the provided router state,\n          // so we'll just use the params from the route matcher\n        }\n\n        const { parsedDestination, destQuery } = prepareDestination({\n          appendParamsToQuery: true,\n          destination: rewrite.destination,\n          params: params,\n          query: parsedUrl.query,\n        })\n\n        // if the rewrite destination is external break rewrite chain\n        if (parsedDestination.protocol) {\n          return true\n        }\n\n        Object.assign(rewriteParams, destQuery, params)\n        Object.assign(parsedUrl.query, parsedDestination.query)\n        delete (parsedDestination as any).query\n\n        // for each property in parsedUrl.query, if the value is parametrized (eg :foo), look up the value\n        // in rewriteParams and replace the parametrized value with the actual value\n        // this is used when the rewrite destination does not contain the original source param\n        // and so the value is still parametrized and needs to be replaced with the actual rewrite param\n        Object.entries(parsedUrl.query).forEach(([key, value]) => {\n          if (value && typeof value === 'string' && value.startsWith(':')) {\n            const paramName = value.slice(1)\n            const actualValue = rewriteParams[paramName]\n            if (actualValue) {\n              parsedUrl.query[key] = actualValue\n            }\n          }\n        })\n\n        Object.assign(parsedUrl, parsedDestination)\n\n        fsPathname = parsedUrl.pathname\n        if (!fsPathname) return false\n\n        if (basePath) {\n          fsPathname = fsPathname.replace(new RegExp(`^${basePath}`), '') || '/'\n        }\n\n        if (i18n) {\n          const result = normalizeLocalePath(fsPathname, i18n.locales)\n          fsPathname = result.pathname\n          parsedUrl.query.nextInternalLocale =\n            result.detectedLocale || params.nextInternalLocale\n        }\n\n        if (fsPathname === page) {\n          return true\n        }\n\n        if (pageIsDynamic && dynamicRouteMatcher) {\n          const dynamicParams = dynamicRouteMatcher(fsPathname)\n          if (dynamicParams) {\n            parsedUrl.query = {\n              ...parsedUrl.query,\n              ...dynamicParams,\n            }\n            return true\n          }\n        }\n      }\n      return false\n    }\n\n    for (const rewrite of rewrites.beforeFiles || []) {\n      checkRewrite(rewrite)\n    }\n\n    if (fsPathname !== page) {\n      let finished = false\n\n      for (const rewrite of rewrites.afterFiles || []) {\n        finished = checkRewrite(rewrite)\n        if (finished) break\n      }\n\n      if (!finished && !matchesPage()) {\n        for (const rewrite of rewrites.fallback || []) {\n          finished = checkRewrite(rewrite)\n          if (finished) break\n        }\n      }\n    }\n    return rewriteParams\n  }\n\n  function getParamsFromRouteMatches(routeMatchesHeader: string) {\n    // If we don't have a default route regex, we can't get params from route\n    // matches\n    if (!defaultRouteRegex) return null\n\n    const { groups, routeKeys } = defaultRouteRegex\n\n    const matcher = getRouteMatcher({\n      re: {\n        // Simulate a RegExp match from the \\`req.url\\` input\n        exec: (str: string) => {\n          // Normalize all the prefixed query params.\n          const obj: Record<string, string> = Object.fromEntries(\n            new URLSearchParams(str)\n          )\n          for (const [key, value] of Object.entries(obj)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            obj[normalizedKey] = value\n            delete obj[key]\n          }\n\n          // Use all the named route keys.\n          const result = {} as RegExpExecArray\n          for (const keyName of Object.keys(routeKeys)) {\n            const paramName = routeKeys[keyName]\n\n            // If this param name is not a valid parameter name, then skip it.\n            if (!paramName) continue\n\n            const group = groups[paramName]\n            const value = obj[keyName]\n\n            // When we're missing a required param, we can't match the route.\n            if (!group.optional && !value) return null\n\n            result[group.pos] = value\n          }\n\n          return result\n        },\n      },\n      groups,\n    })\n\n    const routeMatches = matcher(routeMatchesHeader)\n    if (!routeMatches) return null\n\n    return routeMatches\n  }\n\n  function normalizeQueryParams(\n    query: Record<string, string | string[] | undefined>,\n    routeParamKeys: Set<string>\n  ) {\n    // this is used to pass query information in rewrites\n    // but should not be exposed in final query\n    delete query['nextInternalLocale']\n\n    for (const [key, value] of Object.entries(query)) {\n      const normalizedKey = normalizeNextQueryParam(key)\n      if (!normalizedKey) continue\n\n      // Remove the prefixed key from the query params because we want\n      // to consume it for the dynamic route matcher.\n      delete query[key]\n      routeParamKeys.add(normalizedKey)\n\n      if (typeof value === 'undefined') continue\n\n      query[normalizedKey] = Array.isArray(value)\n        ? value.map((v) => decodeQueryPathParameter(v))\n        : decodeQueryPathParameter(value)\n    }\n  }\n\n  return {\n    handleRewrites,\n    defaultRouteRegex,\n    dynamicRouteMatcher,\n    defaultRouteMatches,\n    normalizeQueryParams,\n    getParamsFromRouteMatches,\n    /**\n     * Normalize dynamic route params.\n     *\n     * @param query - The query params to normalize.\n     * @param ignoreMissingOptional - Whether to ignore missing optional params.\n     * @returns The normalized params and whether they are valid.\n     */\n    normalizeDynamicRouteParams: (\n      query: ParsedUrlQuery,\n      ignoreMissingOptional: boolean\n    ) => {\n      if (!defaultRouteRegex || !defaultRouteMatches) {\n        return { params: {}, hasValidParams: false }\n      }\n\n      return normalizeDynamicRouteParams(\n        query,\n        defaultRouteRegex,\n        defaultRouteMatches,\n        ignoreMissingOptional\n      )\n    },\n\n    normalizeCdnUrl: (\n      req: BaseNextRequest | IncomingMessage,\n      paramKeys: string[]\n    ) => normalizeCdnUrl(req, paramKeys),\n\n    interpolateDynamicPath: (\n      pathname: string,\n      params: Record<string, undefined | string | string[]>\n    ) => interpolateDynamicPath(pathname, params, defaultRouteRegex),\n\n    filterInternalQuery: (query: ParsedUrlQuery, paramKeys: string[]) =>\n      filterInternalQuery(query, paramKeys),\n  }\n}\n\nexport function getPreviouslyRevalidatedTags(\n  headers: IncomingHttpHeaders,\n  previewModeId: string | undefined\n): string[] {\n  return typeof headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER] === 'string' &&\n    headers[NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER] === previewModeId\n    ? headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(',')\n    : []\n}\n"], "names": ["normalizeLocalePath", "getPathMatch", "getNamedRouteRegex", "getRouteMatcher", "matchHas", "prepareDestination", "removeTrailingSlash", "normalizeRscURL", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "normalizeNextQueryParam", "decodeQueryPathParameter", "parseReqUrl", "formatUrl", "parseAndValidateFlightRouterState", "isInterceptionRouteRewrite", "NEXT_ROUTER_STATE_TREE_HEADER", "getSelectedParams", "filterInternalQuery", "query", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "isNextQueryPrefix", "startsWith", "isNextInterceptionMarkerPrefix", "includes", "normalizeCdnUrl", "req", "_parsedUrl", "url", "search", "interpolateDynamicPath", "pathname", "params", "defaultRouteRegex", "param", "Object", "keys", "groups", "optional", "repeat", "builtParam", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "replaceAll", "normalizeDynamicRouteParams", "defaultRouteMatches", "ignoreMissingOptional", "hasValidParams", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "val", "length", "undefined", "split", "getServerUtils", "page", "i18n", "basePath", "rewrites", "pageIsDynamic", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "prefixRouteKeys", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "checkRewrite", "rewrite", "matcher", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "assign", "<PERSON><PERSON><PERSON><PERSON>", "headers", "toLowerCase", "err", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appendParamsToQuery", "destination", "protocol", "entries", "for<PERSON>ach", "paramName", "slice", "actualValue", "replace", "RegExp", "result", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "routeMatchesHeader", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "normalizedKey", "keyName", "group", "pos", "routeMatches", "normalizeQueryParams", "routeParamKeys", "add", "getPreviouslyRevalidatedTags", "previewModeId"], "mappings": "AAOA,SAASA,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SACEC,QAAQ,EACRC,kBAAkB,QACb,iDAAgD;AACvD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,eAAe,QAAQ,uCAAsC;AACtE,SACEC,sCAAsC,EACtCC,kCAAkC,EAClCC,+BAA+B,EAC/BC,uBAAuB,QAClB,mBAAkB;AACzB,SAASC,uBAAuB,QAAQ,cAAa;AAErD,SAASC,wBAAwB,QAAQ,oCAAmC;AAE5E,SAASC,WAAW,QAAQ,aAAY;AACxC,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,iCAAiC,QAAQ,sDAAqD;AACvG,SAASC,0BAA0B,QAAQ,+CAA8C;AACzF,SAASC,6BAA6B,QAAQ,0CAAyC;AACvF,SAASC,iBAAiB,QAAQ,2DAA0D;AAE5F,SAASC,oBACPC,KAAoD,EACpDC,SAAmB;IAEnB,qDAAqD;IACrD,2CAA2C;IAC3C,OAAOD,KAAK,CAAC,qBAAqB;IAElC,IAAK,MAAME,OAAOF,MAAO;QACvB,MAAMG,oBACJD,QAAQZ,2BAA2BY,IAAIE,UAAU,CAACd;QAEpD,MAAMe,iCACJH,QAAQb,mCACRa,IAAIE,UAAU,CAACf;QAEjB,IACEc,qBACAE,kCACAJ,UAAUK,QAAQ,CAACJ,MACnB;YACA,OAAOF,KAAK,CAACE,IAAI;QACnB;IACF;AACF;AAEA,OAAO,SAASK,gBACdC,GAAsC,EACtCP,SAAmB;IAEnB,wEAAwE;IACxE,uDAAuD;IACvD,MAAMQ,aAAahB,YAAYe,IAAIE,GAAG;IAEtC,uCAAuC;IACvC,IAAI,CAACD,YAAY;QACf,OAAOD,IAAIE,GAAG;IAChB;IACA,OAAO,AAACD,WAAmBE,MAAM;IACjCZ,oBAAoBU,WAAWT,KAAK,EAAEC;IAEtCO,IAAIE,GAAG,GAAGhB,UAAUe;AACtB;AAEA,OAAO,SAASG,uBACdC,QAAgB,EAChBC,MAAsB,EACtBC,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOF;IAE/B,KAAK,MAAMG,SAASC,OAAOC,IAAI,CAACH,kBAAkBI,MAAM,EAAG;QACzD,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGN,kBAAkBI,MAAM,CAACH,MAAM;QAC5D,IAAIM,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,KAAKL,MAAM,CAAC,CAAC;QAEnD,IAAII,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,IAAIC;QACJ,MAAMC,QAAQV,MAAM,CAACE,MAAM;QAE3B,IAAIS,MAAMC,OAAO,CAACF,QAAQ;YACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;QACjE,OAAO,IAAIN,OAAO;YAChBD,aAAaM,mBAAmBL;QAClC,OAAO;YACLD,aAAa;QACf;QAEA,IAAIA,cAAcH,UAAU;YAC1BP,WAAWA,SAASkB,UAAU,CAACT,YAAYC;QAC7C;IACF;IAEA,OAAOV;AACT;AAEA,OAAO,SAASmB,4BACdhC,KAAqB,EACrBe,iBAAwD,EACxDkB,mBAAmC,EACnCC,qBAA8B;IAE9B,IAAIC,iBAAiB;IACrB,IAAIrB,SAAyB,CAAC;IAE9B,KAAK,MAAMZ,OAAOe,OAAOC,IAAI,CAACH,kBAAkBI,MAAM,EAAG;QACvD,IAAIK,QAAuCxB,KAAK,CAACE,IAAI;QAErD,IAAI,OAAOsB,UAAU,UAAU;YAC7BA,QAAQtC,gBAAgBsC;QAC1B,OAAO,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YAC/BA,QAAQA,MAAMG,GAAG,CAACzC;QACpB;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMkD,eAAeH,mBAAoB,CAAC/B,IAAI;QAC9C,MAAMmC,aAAatB,kBAAmBI,MAAM,CAACjB,IAAI,CAACkB,QAAQ;QAE1D,MAAMkB,iBAAiBb,MAAMC,OAAO,CAACU,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOf,MAAMC,OAAO,CAACF,SACjBA,MAAMe,IAAI,CAAC,CAACE,MAAQA,IAAInC,QAAQ,CAACkC,eACjChB,yBAAAA,MAAOlB,QAAQ,CAACkC;QACtB,KACAhB,yBAAAA,MAAOlB,QAAQ,CAAC8B;QAEpB,IACEE,kBACC,OAAOd,UAAU,eAAe,CAAEa,CAAAA,cAAcH,qBAAoB,GACrE;YACA,OAAO;gBAAEpB,QAAQ,CAAC;gBAAGqB,gBAAgB;YAAM;QAC7C;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEE,cACC,CAAA,CAACb,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMkB,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9ClB,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAEtB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAsB,QAAQmB;YACR,OAAO3C,KAAK,CAACE,IAAI;QACnB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEsB,SACA,OAAOA,UAAU,YACjBT,kBAAmBI,MAAM,CAACjB,IAAI,CAACmB,MAAM,EACrC;YACAG,QAAQA,MAAMoB,KAAK,CAAC;QACtB;QAEA,IAAIpB,OAAO;YACTV,MAAM,CAACZ,IAAI,GAAGsB;QAChB;IACF;IAEA,OAAO;QACLV;QACAqB;IACF;AACF;AAEA,OAAO,SAASU,eAAe,EAC7BC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,aAAa,EAad;IACC,IAAIrC;IACJ,IAAIsC;IACJ,IAAIpB;IAEJ,IAAIiB,eAAe;QACjBnC,oBAAoBlC,mBAAmBiE,MAAM;YAC3CQ,iBAAiB;QACnB;QACAD,sBAAsBvE,gBAAgBiC;QACtCkB,sBAAsBoB,oBAAoBP;IAC5C;IAEA,SAASS,eACP/C,GAAsC,EACtCgD,SAA6B;QAE7B,MAAMC,gBAAwC,CAAC;QAC/C,IAAIC,aAAaF,UAAU3C,QAAQ;QAEnC,MAAM8C,cAAc;YAClB,MAAMC,oBAAoB3E,oBAAoByE,cAAc;YAC5D,OACEE,sBAAsB3E,oBAAoB6D,UAC1CO,uCAAAA,oBAAsBO;QAE1B;QAEA,MAAMC,eAAe,CAACC;YACpB,MAAMC,UAAUnF,aACdkF,QAAQE,MAAM,GAAIb,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEc,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACf;YACf;YAGF,IAAI,CAACI,UAAU3C,QAAQ,EAAE,OAAO;YAEhC,IAAIC,SAASiD,QAAQP,UAAU3C,QAAQ;YAEvC,IAAI,AAACiD,CAAAA,QAAQM,GAAG,IAAIN,QAAQO,OAAO,AAAD,KAAMvD,QAAQ;gBAC9C,MAAMwD,YAAYvF,SAChByB,KACAgD,UAAUxD,KAAK,EACf8D,QAAQM,GAAG,EACXN,QAAQO,OAAO;gBAGjB,IAAIC,WAAW;oBACbrD,OAAOsD,MAAM,CAACzD,QAAQwD;gBACxB,OAAO;oBACLxD,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,IAAI;oBACF,+EAA+E;oBAC/E,gFAAgF;oBAChF,8EAA8E;oBAC9E,IAAIlB,2BAA2BkE,UAAqB;wBAClD,MAAMU,cACJhE,IAAIiE,OAAO,CAAC5E,8BAA8B6E,WAAW,GAAG;wBAE1D,IAAIF,aAAa;4BACf1D,SAAS;gCACP,GAAGhB,kBACDH,kCAAkC6E,aACnC;gCACD,GAAG1D,MAAM;4BACX;wBACF;oBACF;gBACF,EAAE,OAAO6D,KAAK;gBACZ,wFAAwF;gBACxF,sDAAsD;gBACxD;gBAEA,MAAM,EAAEC,iBAAiB,EAAEC,SAAS,EAAE,GAAG7F,mBAAmB;oBAC1D8F,qBAAqB;oBACrBC,aAAajB,QAAQiB,WAAW;oBAChCjE,QAAQA;oBACRd,OAAOwD,UAAUxD,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAI4E,kBAAkBI,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEA/D,OAAOsD,MAAM,CAACd,eAAeoB,WAAW/D;gBACxCG,OAAOsD,MAAM,CAACf,UAAUxD,KAAK,EAAE4E,kBAAkB5E,KAAK;gBACtD,OAAO,AAAC4E,kBAA0B5E,KAAK;gBAEvC,kGAAkG;gBAClG,4EAA4E;gBAC5E,uFAAuF;gBACvF,gGAAgG;gBAChGiB,OAAOgE,OAAO,CAACzB,UAAUxD,KAAK,EAAEkF,OAAO,CAAC,CAAC,CAAChF,KAAKsB,MAAM;oBACnD,IAAIA,SAAS,OAAOA,UAAU,YAAYA,MAAMpB,UAAU,CAAC,MAAM;wBAC/D,MAAM+E,YAAY3D,MAAM4D,KAAK,CAAC;wBAC9B,MAAMC,cAAc5B,aAAa,CAAC0B,UAAU;wBAC5C,IAAIE,aAAa;4BACf7B,UAAUxD,KAAK,CAACE,IAAI,GAAGmF;wBACzB;oBACF;gBACF;gBAEApE,OAAOsD,MAAM,CAACf,WAAWoB;gBAEzBlB,aAAaF,UAAU3C,QAAQ;gBAC/B,IAAI,CAAC6C,YAAY,OAAO;gBAExB,IAAIV,UAAU;oBACZU,aAAaA,WAAW4B,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEvC,UAAU,GAAG,OAAO;gBACrE;gBAEA,IAAID,MAAM;oBACR,MAAMyC,SAAS7G,oBAAoB+E,YAAYX,KAAK0C,OAAO;oBAC3D/B,aAAa8B,OAAO3E,QAAQ;oBAC5B2C,UAAUxD,KAAK,CAAC0F,kBAAkB,GAChCF,OAAOG,cAAc,IAAI7E,OAAO4E,kBAAkB;gBACtD;gBAEA,IAAIhC,eAAeZ,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAII,iBAAiBG,qBAAqB;oBACxC,MAAMuC,gBAAgBvC,oBAAoBK;oBAC1C,IAAIkC,eAAe;wBACjBpC,UAAUxD,KAAK,GAAG;4BAChB,GAAGwD,UAAUxD,KAAK;4BAClB,GAAG4F,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAM9B,WAAWb,SAAS4C,WAAW,IAAI,EAAE,CAAE;YAChDhC,aAAaC;QACf;QAEA,IAAIJ,eAAeZ,MAAM;YACvB,IAAIgD,WAAW;YAEf,KAAK,MAAMhC,WAAWb,SAAS8C,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAWjC,aAAaC;gBACxB,IAAIgC,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAACnC,eAAe;gBAC/B,KAAK,MAAMG,WAAWb,SAAS+C,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAWjC,aAAaC;oBACxB,IAAIgC,UAAU;gBAChB;YACF;QACF;QACA,OAAOrC;IACT;IAEA,SAASwC,0BAA0BC,kBAA0B;QAC3D,yEAAyE;QACzE,UAAU;QACV,IAAI,CAACnF,mBAAmB,OAAO;QAE/B,MAAM,EAAEI,MAAM,EAAEgF,SAAS,EAAE,GAAGpF;QAE9B,MAAMgD,UAAUjF,gBAAgB;YAC9BsH,IAAI;gBACF,qDAAqD;gBACrDC,MAAM,CAACC;oBACL,2CAA2C;oBAC3C,MAAMC,MAA8BtF,OAAOuF,WAAW,CACpD,IAAIC,gBAAgBH;oBAEtB,KAAK,MAAM,CAACpG,KAAKsB,MAAM,IAAIP,OAAOgE,OAAO,CAACsB,KAAM;wBAC9C,MAAMG,gBAAgBnH,wBAAwBW;wBAC9C,IAAI,CAACwG,eAAe;wBAEpBH,GAAG,CAACG,cAAc,GAAGlF;wBACrB,OAAO+E,GAAG,CAACrG,IAAI;oBACjB;oBAEA,gCAAgC;oBAChC,MAAMsF,SAAS,CAAC;oBAChB,KAAK,MAAMmB,WAAW1F,OAAOC,IAAI,CAACiF,WAAY;wBAC5C,MAAMhB,YAAYgB,SAAS,CAACQ,QAAQ;wBAEpC,kEAAkE;wBAClE,IAAI,CAACxB,WAAW;wBAEhB,MAAMyB,QAAQzF,MAAM,CAACgE,UAAU;wBAC/B,MAAM3D,QAAQ+E,GAAG,CAACI,QAAQ;wBAE1B,iEAAiE;wBACjE,IAAI,CAACC,MAAMxF,QAAQ,IAAI,CAACI,OAAO,OAAO;wBAEtCgE,MAAM,CAACoB,MAAMC,GAAG,CAAC,GAAGrF;oBACtB;oBAEA,OAAOgE;gBACT;YACF;YACArE;QACF;QAEA,MAAM2F,eAAe/C,QAAQmC;QAC7B,IAAI,CAACY,cAAc,OAAO;QAE1B,OAAOA;IACT;IAEA,SAASC,qBACP/G,KAAoD,EACpDgH,cAA2B;QAE3B,qDAAqD;QACrD,2CAA2C;QAC3C,OAAOhH,KAAK,CAAC,qBAAqB;QAElC,KAAK,MAAM,CAACE,KAAKsB,MAAM,IAAIP,OAAOgE,OAAO,CAACjF,OAAQ;YAChD,MAAM0G,gBAAgBnH,wBAAwBW;YAC9C,IAAI,CAACwG,eAAe;YAEpB,gEAAgE;YAChE,+CAA+C;YAC/C,OAAO1G,KAAK,CAACE,IAAI;YACjB8G,eAAeC,GAAG,CAACP;YAEnB,IAAI,OAAOlF,UAAU,aAAa;YAElCxB,KAAK,CAAC0G,cAAc,GAAGjF,MAAMC,OAAO,CAACF,SACjCA,MAAMG,GAAG,CAAC,CAACC,IAAMpC,yBAAyBoC,MAC1CpC,yBAAyBgC;QAC/B;IACF;IAEA,OAAO;QACL+B;QACAxC;QACAsC;QACApB;QACA8E;QACAd;QACA;;;;;;KAMC,GACDjE,6BAA6B,CAC3BhC,OACAkC;YAEA,IAAI,CAACnB,qBAAqB,CAACkB,qBAAqB;gBAC9C,OAAO;oBAAEnB,QAAQ,CAAC;oBAAGqB,gBAAgB;gBAAM;YAC7C;YAEA,OAAOH,4BACLhC,OACAe,mBACAkB,qBACAC;QAEJ;QAEA3B,iBAAiB,CACfC,KACAP,YACGM,gBAAgBC,KAAKP;QAE1BW,wBAAwB,CACtBC,UACAC,SACGF,uBAAuBC,UAAUC,QAAQC;QAE9ChB,qBAAqB,CAACC,OAAuBC,YAC3CF,oBAAoBC,OAAOC;IAC/B;AACF;AAEA,OAAO,SAASiH,6BACdzC,OAA4B,EAC5B0C,aAAiC;IAEjC,OAAO,OAAO1C,OAAO,CAACrF,mCAAmC,KAAK,YAC5DqF,OAAO,CAACtF,uCAAuC,KAAKgI,gBAClD1C,OAAO,CAACrF,mCAAmC,CAACwD,KAAK,CAAC,OAClD,EAAE;AACR", "ignoreList": [0]}