{"version": 3, "sources": ["../../src/server/config.ts"], "sourcesContent": ["import { existsSync } from 'fs'\nimport { basename, extname, join, relative, isAbsolute, resolve } from 'path'\nimport { pathToFileURL } from 'url'\nimport findUp from 'next/dist/compiled/find-up'\nimport * as Log from '../build/output/log'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  CONFIG_FILES,\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_EXPORT,\n  PHASE_PRODUCTION_BUILD,\n  PHASE_PRODUCTION_SERVER,\n} from '../shared/lib/constants'\nimport { defaultConfig, normalizeConfig } from './config-shared'\nimport type {\n  ExperimentalConfig,\n  NextConfigComplete,\n  NextConfig,\n  TurbopackLoaderItem,\n  NextAdapter,\n} from './config-shared'\n\nimport { loadWebpackHook } from './config-utils'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport type { ImageConfig } from '../shared/lib/image-config'\nimport { loadEnvConfig, updateInitialEnv } from '@next/env'\nimport { flushAndExit } from '../telemetry/flush-and-exit'\nimport { findRootDir } from '../lib/find-root'\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\nimport { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\nimport { matchRemotePattern } from '../shared/lib/match-remote-pattern'\n\nimport type { ZodError } from 'next/dist/compiled/zod'\nimport { hasNextSupport } from '../server/ci-info'\nimport { transpileConfig } from '../build/next-config-ts/transpile-config'\nimport { dset } from '../shared/lib/dset'\nimport { normalizeZodErrors } from '../shared/lib/zod'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport { findDir } from '../lib/find-pages-dir'\nimport { CanaryOnlyError, isStableBuild } from '../shared/lib/canary-only'\nimport { interopDefault } from '../lib/interop-default'\n\nexport { normalizeConfig } from './config-shared'\nexport type { DomainLocale, NextConfig } from './config-shared'\n\nfunction normalizeNextConfigZodErrors(\n  error: ZodError<NextConfig>\n): [errorMessages: string[], shouldExit: boolean] {\n  let shouldExit = false\n  const issues = normalizeZodErrors(error)\n  return [\n    issues.flatMap(({ issue, message }) => {\n      if (issue.path[0] === 'images') {\n        // We exit the build when encountering an error in the images config\n        shouldExit = true\n      }\n\n      return message\n    }),\n    shouldExit,\n  ]\n}\n\nexport function warnOptionHasBeenDeprecated(\n  config: NextConfig,\n  nestedPropertyKey: string,\n  reason: string,\n  silent: boolean\n): boolean {\n  let hasWarned = false\n  if (!silent) {\n    let current = config\n    let found = true\n    const nestedPropertyKeys = nestedPropertyKey.split('.')\n    for (const key of nestedPropertyKeys) {\n      if (current[key] !== undefined) {\n        current = current[key]\n      } else {\n        found = false\n        break\n      }\n    }\n    if (found) {\n      Log.warnOnce(reason)\n      hasWarned = true\n    }\n  }\n  return hasWarned\n}\n\nexport function warnOptionHasBeenMovedOutOfExperimental(\n  config: NextConfig,\n  oldExperimentalKey: string,\n  newKey: string,\n  configFileName: string,\n  silent: boolean\n) {\n  if (config.experimental && oldExperimentalKey in config.experimental) {\n    if (!silent) {\n      Log.warn(\n        `\\`experimental.${oldExperimentalKey}\\` has been moved to \\`${newKey}\\`. ` +\n          `Please update your ${configFileName} file accordingly.`\n      )\n    }\n\n    let current = config\n    const newKeys = newKey.split('.')\n    while (newKeys.length > 1) {\n      const key = newKeys.shift()!\n      current[key] = current[key] || {}\n      current = current[key]\n    }\n    current[newKeys.shift()!] = (config.experimental as any)[oldExperimentalKey]\n  }\n\n  return config\n}\n\nfunction warnCustomizedOption(\n  config: NextConfig,\n  key: string,\n  defaultValue: any,\n  customMessage: string,\n  configFileName: string,\n  silent: boolean\n) {\n  const segs = key.split('.')\n  let current = config\n\n  while (segs.length >= 1) {\n    const seg = segs.shift()!\n    if (!(seg in current)) {\n      return\n    }\n    current = current[seg]\n  }\n\n  if (!silent && current !== defaultValue) {\n    Log.warn(\n      `The \"${key}\" option has been modified. ${customMessage ? customMessage + '. ' : ''}It should be removed from your ${configFileName}.`\n    )\n  }\n}\n\nfunction assignDefaults(\n  dir: string,\n  userConfig: NextConfig & { configFileName: string },\n  silent: boolean\n): NextConfigComplete {\n  const configFileName = userConfig.configFileName\n  if (typeof userConfig.exportTrailingSlash !== 'undefined') {\n    if (!silent) {\n      Log.warn(\n        `The \"exportTrailingSlash\" option has been renamed to \"trailingSlash\". Please update your ${configFileName}.`\n      )\n    }\n    if (typeof userConfig.trailingSlash === 'undefined') {\n      userConfig.trailingSlash = userConfig.exportTrailingSlash\n    }\n    delete userConfig.exportTrailingSlash\n  }\n\n  const config = Object.keys(userConfig).reduce<{ [key: string]: any }>(\n    (currentConfig, key) => {\n      const value = userConfig[key]\n\n      if (value === undefined || value === null) {\n        return currentConfig\n      }\n\n      if (key === 'distDir') {\n        if (typeof value !== 'string') {\n          throw new Error(\n            `Specified distDir is not a string, found type \"${typeof value}\"`\n          )\n        }\n        const userDistDir = value.trim()\n\n        // don't allow public as the distDir as this is a reserved folder for\n        // public files\n        if (userDistDir === 'public') {\n          throw new Error(\n            `The 'public' directory is reserved in Next.js and can not be set as the 'distDir'. https://nextjs.org/docs/messages/can-not-output-to-public`\n          )\n        }\n        // make sure distDir isn't an empty string as it can result in the provided\n        // directory being deleted in development mode\n        if (userDistDir.length === 0) {\n          throw new Error(\n            `Invalid distDir provided, distDir can not be an empty string. Please remove this config or set it to undefined`\n          )\n        }\n      }\n\n      if (key === 'pageExtensions') {\n        if (!Array.isArray(value)) {\n          throw new Error(\n            `Specified pageExtensions is not an array of strings, found \"${value}\". Please update this config or remove it.`\n          )\n        }\n\n        if (!value.length) {\n          throw new Error(\n            `Specified pageExtensions is an empty array. Please update it with the relevant extensions or remove it.`\n          )\n        }\n\n        value.forEach((ext) => {\n          if (typeof ext !== 'string') {\n            throw new Error(\n              `Specified pageExtensions is not an array of strings, found \"${ext}\" of type \"${typeof ext}\". Please update this config or remove it.`\n            )\n          }\n        })\n      }\n\n      const defaultValue = (defaultConfig as Record<string, unknown>)[key]\n\n      if (\n        !!value &&\n        value.constructor === Object &&\n        typeof defaultValue === 'object'\n      ) {\n        currentConfig[key] = {\n          ...defaultValue,\n          ...Object.keys(value).reduce<any>((c, k) => {\n            const v = value[k]\n            if (v !== undefined && v !== null) {\n              c[k] = v\n            }\n            return c\n          }, {}),\n        }\n      } else {\n        currentConfig[key] = value\n      }\n\n      return currentConfig\n    },\n    {}\n  ) as NextConfig & { configFileName: string }\n\n  // TODO: remove these once we've made PPR default\n  // If this was defaulted to true, it implies that the configuration was\n  // overridden for testing to be defaulted on.\n  if (defaultConfig.experimental?.ppr) {\n    Log.warn(\n      `\\`experimental.ppr\\` has been defaulted to \\`true\\` because \\`__NEXT_EXPERIMENTAL_PPR\\` was set to \\`true\\` during testing.`\n    )\n  }\n\n  if (defaultConfig.experimental?.dynamicIO) {\n    Log.warn(\n      `\\`experimental.dynamicIO\\` has been defaulted to \\`true\\` because \\`__NEXT_EXPERIMENTAL_CACHE_COMPONENTS\\` was set to \\`true\\` during testing.`\n    )\n  }\n\n  const result = {\n    ...defaultConfig,\n    ...config,\n    experimental: {\n      ...defaultConfig.experimental,\n      ...config.experimental,\n    },\n  }\n\n  // ensure correct default is set for api-resolver revalidate handling\n  if (!result.experimental?.trustHostHeader && ciEnvironment.hasNextSupport) {\n    result.experimental.trustHostHeader = true\n  }\n\n  if (\n    result.experimental?.allowDevelopmentBuild &&\n    process.env.NODE_ENV !== 'development'\n  ) {\n    throw new Error(\n      `The experimental.allowDevelopmentBuild option requires NODE_ENV to be explicitly set to 'development'.`\n    )\n  }\n\n  if (isStableBuild()) {\n    // Prevents usage of certain experimental features outside of canary\n    if (result.experimental?.ppr) {\n      throw new CanaryOnlyError({ feature: 'experimental.ppr' })\n    } else if (result.experimental?.dynamicIO) {\n      throw new CanaryOnlyError({ feature: 'experimental.dynamicIO' })\n    } else if (result.experimental?.turbopackPersistentCaching) {\n      throw new CanaryOnlyError({\n        feature: 'experimental.turbopackPersistentCaching',\n      })\n    } else if (result.experimental?.nodeMiddleware) {\n      throw new CanaryOnlyError({ feature: 'experimental.nodeMiddleware' })\n    }\n  }\n\n  if (result.output === 'export') {\n    if (result.i18n) {\n      throw new Error(\n        'Specified \"i18n\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-i18n'\n      )\n    }\n\n    if (!hasNextSupport) {\n      if (result.rewrites) {\n        Log.warn(\n          'Specified \"rewrites\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.redirects) {\n        Log.warn(\n          'Specified \"redirects\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.headers) {\n        Log.warn(\n          'Specified \"headers\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n    }\n  }\n\n  if (typeof result.assetPrefix !== 'string') {\n    throw new Error(\n      `Specified assetPrefix is not a string, found type \"${typeof result.assetPrefix}\" https://nextjs.org/docs/messages/invalid-assetprefix`\n    )\n  }\n\n  if (typeof result.basePath !== 'string') {\n    throw new Error(\n      `Specified basePath is not a string, found type \"${typeof result.basePath}\"`\n    )\n  }\n\n  if (result.basePath !== '') {\n    if (result.basePath === '/') {\n      throw new Error(\n        `Specified basePath /. basePath has to be either an empty string or a path prefix\"`\n      )\n    }\n\n    if (!result.basePath.startsWith('/')) {\n      throw new Error(\n        `Specified basePath has to start with a /, found \"${result.basePath}\"`\n      )\n    }\n\n    if (result.basePath !== '/') {\n      if (result.basePath.endsWith('/')) {\n        throw new Error(\n          `Specified basePath should not end with /, found \"${result.basePath}\"`\n        )\n      }\n\n      if (result.assetPrefix === '') {\n        result.assetPrefix = result.basePath\n      }\n\n      if (result.amp?.canonicalBase === '') {\n        result.amp.canonicalBase = result.basePath\n      }\n    }\n  }\n\n  if (result?.images) {\n    const images: ImageConfig = result.images\n\n    if (typeof images !== 'object') {\n      throw new Error(\n        `Specified images should be an object received ${typeof images}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n      )\n    }\n\n    if (images.localPatterns) {\n      if (!Array.isArray(images.localPatterns)) {\n        throw new Error(\n          `Specified images.localPatterns should be an Array received ${typeof images.localPatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n      // avoid double-pushing the same pattern if it already exists\n      const hasMatch = images.localPatterns.some(\n        (pattern) =>\n          pattern.pathname === '/_next/static/media/**' && pattern.search === ''\n      )\n      if (!hasMatch) {\n        // static import images are automatically allowed\n        images.localPatterns.push({\n          pathname: '/_next/static/media/**',\n          search: '',\n        })\n      }\n    }\n\n    if (images.remotePatterns) {\n      if (!Array.isArray(images.remotePatterns)) {\n        throw new Error(\n          `Specified images.remotePatterns should be an Array received ${typeof images.remotePatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n\n      // We must convert URL to RemotePattern since URL has a colon in the protocol\n      // and also has additional properties we want to filter out. Also, new URL()\n      // accepts any protocol so we need manual validation here.\n      images.remotePatterns = images.remotePatterns.map(\n        ({ protocol, hostname, port, pathname, search }) => {\n          const proto = protocol?.replace(/:$/, '')\n          if (!['http', 'https', undefined].includes(proto)) {\n            throw new Error(\n              `Specified images.remotePatterns must have protocol \"http\" or \"https\" received \"${proto}\".`\n            )\n          }\n          return {\n            protocol: proto as 'http' | 'https' | undefined,\n            hostname,\n            port,\n            pathname,\n            search,\n          }\n        }\n      )\n\n      // static images are automatically prefixed with assetPrefix\n      // so we need to ensure _next/image allows downloading from\n      // this resource\n      if (config.assetPrefix?.startsWith('http')) {\n        try {\n          const url = new URL(config.assetPrefix)\n          const hasMatchForAssetPrefix = images.remotePatterns.some((pattern) =>\n            matchRemotePattern(pattern, url)\n          )\n\n          // avoid double-pushing the same pattern if it already can be matched\n          if (!hasMatchForAssetPrefix) {\n            images.remotePatterns.push({\n              hostname: url.hostname,\n              protocol: url.protocol.replace(/:$/, '') as 'http' | 'https',\n              port: url.port,\n            })\n          }\n        } catch (error) {\n          throw new Error(\n            `Invalid assetPrefix provided. Original error: ${error}`\n          )\n        }\n      }\n    }\n\n    if (images.domains) {\n      if (!Array.isArray(images.domains)) {\n        throw new Error(\n          `Specified images.domains should be an Array received ${typeof images.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n    }\n\n    if (!images.loader) {\n      images.loader = 'default'\n    }\n\n    if (\n      images.loader !== 'default' &&\n      images.loader !== 'custom' &&\n      images.path === imageConfigDefault.path\n    ) {\n      throw new Error(\n        `Specified images.loader property (${images.loader}) also requires images.path property to be assigned to a URL prefix.\\nSee more info here: https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration`\n      )\n    }\n\n    if (\n      images.path === imageConfigDefault.path &&\n      result.basePath &&\n      !pathHasPrefix(images.path, result.basePath)\n    ) {\n      images.path = `${result.basePath}${images.path}`\n    }\n\n    // Append trailing slash for non-default loaders and when trailingSlash is set\n    if (\n      images.path &&\n      !images.path.endsWith('/') &&\n      (images.loader !== 'default' || result.trailingSlash)\n    ) {\n      images.path += '/'\n    }\n\n    if (images.loaderFile) {\n      if (images.loader !== 'default' && images.loader !== 'custom') {\n        throw new Error(\n          `Specified images.loader property (${images.loader}) cannot be used with images.loaderFile property. Please set images.loader to \"custom\".`\n        )\n      }\n      const absolutePath = join(dir, images.loaderFile)\n      if (!existsSync(absolutePath)) {\n        throw new Error(\n          `Specified images.loaderFile does not exist at \"${absolutePath}\".`\n        )\n      }\n      images.loaderFile = absolutePath\n    }\n  }\n\n  if (result.experimental.devtoolNewPanelUI) {\n    result.experimental.devtoolSegmentExplorer = true\n  }\n\n  warnCustomizedOption(\n    result,\n    'experimental.esmExternals',\n    true,\n    'experimental.esmExternals is not recommended to be modified as it may disrupt module resolution',\n    configFileName,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'experimental.instrumentationHook',\n    `\\`experimental.instrumentationHook\\` is no longer needed, because \\`instrumentation.js\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'experimental.after',\n    `\\`experimental.after\\` is no longer needed, because \\`after\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.appIsrStatus',\n    `\\`devIndicators.appIsrStatus\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.buildActivity',\n    `\\`devIndicators.buildActivity\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  const hasWarnedBuildActivityPosition = warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.buildActivityPosition',\n    `\\`devIndicators.buildActivityPosition\\` has been renamed to \\`devIndicators.position\\`. Please update your ${configFileName} file accordingly.`,\n    silent\n  )\n  if (\n    hasWarnedBuildActivityPosition &&\n    result.devIndicators !== false &&\n    'buildActivityPosition' in result.devIndicators &&\n    result.devIndicators.buildActivityPosition !== result.devIndicators.position\n  ) {\n    Log.warnOnce(\n      `The \\`devIndicators\\` option \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") conflicts with \\`position\\` (\"${result.devIndicators.position}\"). Using \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") for backward compatibility.`\n    )\n    result.devIndicators.position = result.devIndicators.buildActivityPosition\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'bundlePagesExternals',\n    'bundlePagesRouterDependencies',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'serverComponentsExternalPackages',\n    'serverExternalPackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'relay',\n    'compiler.relay',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'styledComponents',\n    'compiler.styledComponents',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'emotion',\n    'compiler.emotion',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'reactRemoveProperties',\n    'compiler.reactRemoveProperties',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'removeConsole',\n    'compiler.removeConsole',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'swrDelta',\n    'expireTime',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingRoot',\n    'outputFileTracingRoot',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingIncludes',\n    'outputFileTracingIncludes',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingExcludes',\n    'outputFileTracingExcludes',\n    configFileName,\n    silent\n  )\n\n  if ((result.experimental as any).outputStandalone) {\n    if (!silent) {\n      Log.warn(\n        `experimental.outputStandalone has been renamed to \"output: 'standalone'\", please move the config.`\n      )\n    }\n    result.output = 'standalone'\n  }\n\n  if (\n    typeof result.experimental?.serverActions?.bodySizeLimit !== 'undefined'\n  ) {\n    const value = parseInt(\n      result.experimental.serverActions?.bodySizeLimit.toString()\n    )\n    if (isNaN(value) || value < 1) {\n      throw new Error(\n        'Server Actions Size Limit must be a valid number or filesize format larger than 1MB: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit'\n      )\n    }\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'transpilePackages',\n    'transpilePackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipMiddlewareUrlNormalize',\n    'skipMiddlewareUrlNormalize',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipTrailingSlashRedirect',\n    'skipTrailingSlashRedirect',\n    configFileName,\n    silent\n  )\n\n  if (\n    result?.outputFileTracingRoot &&\n    !isAbsolute(result.outputFileTracingRoot)\n  ) {\n    result.outputFileTracingRoot = resolve(result.outputFileTracingRoot)\n    if (!silent) {\n      Log.warn(\n        `outputFileTracingRoot should be absolute, using: ${result.outputFileTracingRoot}`\n      )\n    }\n  }\n\n  if (result?.turbopack?.root && !isAbsolute(result.turbopack.root)) {\n    result.turbopack.root = resolve(result.turbopack.root)\n    if (!silent) {\n      Log.warn(\n        `turbopack.root should be absolute, using: ${result.turbopack.root}`\n      )\n    }\n  }\n\n  // only leverage deploymentId\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    result.deploymentId = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  if (result?.outputFileTracingRoot && !result?.turbopack?.root) {\n    dset(result, ['turbopack', 'root'], result.outputFileTracingRoot)\n  }\n\n  // use the highest level lockfile as tracing root\n  if (!result?.outputFileTracingRoot || !result?.turbopack?.root) {\n    let rootDir = findRootDir(dir)\n\n    if (rootDir) {\n      if (!result?.outputFileTracingRoot) {\n        result.outputFileTracingRoot = rootDir\n      }\n\n      if (!result?.turbopack?.root) {\n        dset(result, ['turbopack', 'root'], rootDir)\n      }\n    }\n  }\n\n  setHttpClientAndAgentOptions(result || defaultConfig)\n\n  if (result.i18n) {\n    const hasAppDir = Boolean(findDir(dir, 'app'))\n\n    if (hasAppDir) {\n      warnOptionHasBeenDeprecated(\n        result,\n        'i18n',\n        `i18n configuration in ${configFileName} is unsupported in App Router.\\nLearn more about internationalization in App Router: https://nextjs.org/docs/app/building-your-application/routing/internationalization`,\n        silent\n      )\n    }\n\n    const { i18n } = result\n    const i18nType = typeof i18n\n\n    if (i18nType !== 'object') {\n      throw new Error(\n        `Specified i18n should be an object received ${i18nType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales should be an Array received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.locales.length > 100 && !silent) {\n      Log.warn(\n        `Received ${i18n.locales.length} i18n.locales items which exceeds the recommended max of 100.\\nSee more info here: https://nextjs.org/docs/advanced-features/i18n-routing#how-does-this-work-with-static-generation`\n      )\n    }\n\n    const defaultLocaleType = typeof i18n.defaultLocale\n\n    if (!i18n.defaultLocale || defaultLocaleType !== 'string') {\n      throw new Error(\n        `Specified i18n.defaultLocale should be a string.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (typeof i18n.domains !== 'undefined' && !Array.isArray(i18n.domains)) {\n      throw new Error(\n        `Specified i18n.domains must be an array of domain objects e.g. [ { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] } ] received ${typeof i18n.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.domains) {\n      const invalidDomainItems = i18n.domains.filter((item) => {\n        if (!item || typeof item !== 'object') return true\n        if (!item.defaultLocale) return true\n        if (!item.domain || typeof item.domain !== 'string') return true\n\n        if (item.domain.includes(':')) {\n          console.warn(\n            `i18n domain: \"${item.domain}\" is invalid it should be a valid domain without protocol (https://) or port (:3000) e.g. example.vercel.sh`\n          )\n          return true\n        }\n\n        const defaultLocaleDuplicate = i18n.domains?.find(\n          (altItem) =>\n            altItem.defaultLocale === item.defaultLocale &&\n            altItem.domain !== item.domain\n        )\n\n        if (!silent && defaultLocaleDuplicate) {\n          console.warn(\n            `Both ${item.domain} and ${defaultLocaleDuplicate.domain} configured the defaultLocale ${item.defaultLocale} but only one can. Change one item's default locale to continue`\n          )\n          return true\n        }\n\n        let hasInvalidLocale = false\n\n        if (Array.isArray(item.locales)) {\n          for (const locale of item.locales) {\n            if (typeof locale !== 'string') hasInvalidLocale = true\n\n            for (const domainItem of i18n.domains || []) {\n              if (domainItem === item) continue\n              if (domainItem.locales && domainItem.locales.includes(locale)) {\n                console.warn(\n                  `Both ${item.domain} and ${domainItem.domain} configured the locale (${locale}) but only one can. Remove it from one i18n.domains config to continue`\n                )\n                hasInvalidLocale = true\n                break\n              }\n            }\n          }\n        }\n\n        return hasInvalidLocale\n      })\n\n      if (invalidDomainItems.length > 0) {\n        throw new Error(\n          `Invalid i18n.domains values:\\n${invalidDomainItems\n            .map((item: any) => JSON.stringify(item))\n            .join(\n              '\\n'\n            )}\\n\\ndomains value must follow format { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] }.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n        )\n      }\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales must be an array of locale strings e.g. [\"en-US\", \"nl-NL\"] received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const invalidLocales = i18n.locales.filter(\n      (locale: any) => typeof locale !== 'string'\n    )\n\n    if (invalidLocales.length > 0) {\n      throw new Error(\n        `Specified i18n.locales contains invalid values (${invalidLocales\n          .map(String)\n          .join(\n            ', '\n          )}), locales must be valid locale tags provided as strings e.g. \"en-US\".\\n` +\n          `See here for list of valid language sub-tags: http://www.iana.org/assignments/language-subtag-registry/language-subtag-registry`\n      )\n    }\n\n    if (!i18n.locales.includes(i18n.defaultLocale)) {\n      throw new Error(\n        `Specified i18n.defaultLocale should be included in i18n.locales.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const normalizedLocales = new Set()\n    const duplicateLocales = new Set()\n\n    i18n.locales.forEach((locale) => {\n      const localeLower = locale.toLowerCase()\n      if (normalizedLocales.has(localeLower)) {\n        duplicateLocales.add(locale)\n      }\n      normalizedLocales.add(localeLower)\n    })\n\n    if (duplicateLocales.size > 0) {\n      throw new Error(\n        `Specified i18n.locales contains the following duplicate locales:\\n` +\n          `${[...duplicateLocales].join(', ')}\\n` +\n          `Each locale should be listed only once.\\n` +\n          `See more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    // make sure default Locale is at the front\n    i18n.locales = [\n      i18n.defaultLocale,\n      ...i18n.locales.filter((locale) => locale !== i18n.defaultLocale),\n    ]\n\n    const localeDetectionType = typeof i18n.localeDetection\n\n    if (\n      localeDetectionType !== 'boolean' &&\n      localeDetectionType !== 'undefined'\n    ) {\n      throw new Error(\n        `Specified i18n.localeDetection should be undefined or a boolean received ${localeDetectionType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n  }\n\n  if (result.devIndicators !== false && result.devIndicators?.position) {\n    const { position } = result.devIndicators\n    const allowedValues = [\n      'top-left',\n      'top-right',\n      'bottom-left',\n      'bottom-right',\n    ]\n\n    if (!allowedValues.includes(position)) {\n      throw new Error(\n        `Invalid \"devIndicator.position\" provided, expected one of ${allowedValues.join(\n          ', '\n        )}, received ${position}`\n      )\n    }\n  }\n\n  if (result.experimental) {\n    result.experimental.cacheLife = {\n      ...defaultConfig.experimental?.cacheLife,\n      ...result.experimental.cacheLife,\n    }\n    const defaultDefault = defaultConfig.experimental?.cacheLife?.['default']\n    if (\n      !defaultDefault ||\n      defaultDefault.revalidate === undefined ||\n      defaultDefault.expire === undefined ||\n      !defaultConfig.experimental?.staleTimes?.static\n    ) {\n      throw new Error('No default cacheLife profile.')\n    }\n    const defaultCacheLifeProfile = result.experimental.cacheLife['default']\n    if (!defaultCacheLifeProfile) {\n      result.experimental.cacheLife['default'] = defaultDefault\n    } else {\n      if (defaultCacheLifeProfile.stale === undefined) {\n        const staticStaleTime = result.experimental.staleTimes?.static\n        defaultCacheLifeProfile.stale =\n          staticStaleTime ?? defaultConfig.experimental?.staleTimes?.static\n      }\n      if (defaultCacheLifeProfile.revalidate === undefined) {\n        defaultCacheLifeProfile.revalidate = defaultDefault.revalidate\n      }\n      if (defaultCacheLifeProfile.expire === undefined) {\n        defaultCacheLifeProfile.expire =\n          result.expireTime ?? defaultDefault.expire\n      }\n    }\n    // This is the most dynamic cache life profile.\n    const secondsCacheLifeProfile = result.experimental.cacheLife['seconds']\n    if (\n      secondsCacheLifeProfile &&\n      secondsCacheLifeProfile.stale === undefined\n    ) {\n      // We default this to whatever stale time you had configured for dynamic content.\n      // Since this is basically a dynamic cache life profile.\n      const dynamicStaleTime = result.experimental.staleTimes?.dynamic\n      secondsCacheLifeProfile.stale =\n        dynamicStaleTime ?? defaultConfig.experimental?.staleTimes?.dynamic\n    }\n  }\n\n  if (result.experimental?.cacheHandlers) {\n    const allowedHandlerNameRegex = /[a-z-]/\n\n    if (typeof result.experimental.cacheHandlers !== 'object') {\n      throw new Error(\n        `Invalid \"experimental.cacheHandlers\" provided, expected an object e.g. { default: '/my-handler.js' }, received ${JSON.stringify(result.experimental.cacheHandlers)}`\n      )\n    }\n\n    const handlerKeys = Object.keys(result.experimental.cacheHandlers)\n    const invalidHandlerItems: Array<{ key: string; reason: string }> = []\n\n    for (const key of handlerKeys) {\n      if (!allowedHandlerNameRegex.test(key)) {\n        invalidHandlerItems.push({\n          key,\n          reason: 'key must only use characters a-z and -',\n        })\n      } else {\n        const handlerPath = (\n          result.experimental.cacheHandlers as {\n            [handlerName: string]: string | undefined\n          }\n        )[key]\n\n        if (handlerPath && !existsSync(handlerPath)) {\n          invalidHandlerItems.push({\n            key,\n            reason: `cache handler path provided does not exist, received ${handlerPath}`,\n          })\n        }\n      }\n      if (invalidHandlerItems.length) {\n        throw new Error(\n          `Invalid handler fields configured for \"experimental.cacheHandler\":\\n${invalidHandlerItems.map((item) => `${key}: ${item.reason}`).join('\\n')}`\n        )\n      }\n    }\n  }\n\n  const userProvidedModularizeImports = result.modularizeImports\n  // Unfortunately these packages end up re-exporting 10600 modules, for example: https://unpkg.com/browse/@mui/icons-material@5.11.16/esm/index.js.\n  // Leveraging modularizeImports tremendously reduces compile times for these.\n  result.modularizeImports = {\n    ...(userProvidedModularizeImports || {}),\n    // This is intentionally added after the user-provided modularizeImports config.\n    '@mui/icons-material': {\n      transform: '@mui/icons-material/{{member}}',\n    },\n    lodash: {\n      transform: 'lodash/{{member}}',\n    },\n  }\n\n  const userProvidedOptimizePackageImports =\n    result.experimental?.optimizePackageImports || []\n\n  result.experimental.optimizePackageImports = [\n    ...new Set([\n      ...userProvidedOptimizePackageImports,\n      'lucide-react',\n      'date-fns',\n      'lodash-es',\n      'ramda',\n      'antd',\n      'react-bootstrap',\n      'ahooks',\n      '@ant-design/icons',\n      '@headlessui/react',\n      '@headlessui-float/react',\n      '@heroicons/react/20/solid',\n      '@heroicons/react/24/solid',\n      '@heroicons/react/24/outline',\n      '@visx/visx',\n      '@tremor/react',\n      'rxjs',\n      '@mui/material',\n      '@mui/icons-material',\n      'recharts',\n      'react-use',\n      'effect',\n      '@effect/schema',\n      '@effect/platform',\n      '@effect/platform-node',\n      '@effect/platform-browser',\n      '@effect/platform-bun',\n      '@effect/sql',\n      '@effect/sql-mssql',\n      '@effect/sql-mysql2',\n      '@effect/sql-pg',\n      '@effect/sql-sqlite-node',\n      '@effect/sql-sqlite-bun',\n      '@effect/sql-sqlite-wasm',\n      '@effect/sql-sqlite-react-native',\n      '@effect/rpc',\n      '@effect/rpc-http',\n      '@effect/typeclass',\n      '@effect/experimental',\n      '@effect/opentelemetry',\n      '@material-ui/core',\n      '@material-ui/icons',\n      '@tabler/icons-react',\n      'mui-core',\n      // We don't support wildcard imports for these configs, e.g. `react-icons/*`\n      // so we need to add them manually.\n      // In the future, we should consider automatically detecting packages that\n      // need to be optimized.\n      'react-icons/ai',\n      'react-icons/bi',\n      'react-icons/bs',\n      'react-icons/cg',\n      'react-icons/ci',\n      'react-icons/di',\n      'react-icons/fa',\n      'react-icons/fa6',\n      'react-icons/fc',\n      'react-icons/fi',\n      'react-icons/gi',\n      'react-icons/go',\n      'react-icons/gr',\n      'react-icons/hi',\n      'react-icons/hi2',\n      'react-icons/im',\n      'react-icons/io',\n      'react-icons/io5',\n      'react-icons/lia',\n      'react-icons/lib',\n      'react-icons/lu',\n      'react-icons/md',\n      'react-icons/pi',\n      'react-icons/ri',\n      'react-icons/rx',\n      'react-icons/si',\n      'react-icons/sl',\n      'react-icons/tb',\n      'react-icons/tfi',\n      'react-icons/ti',\n      'react-icons/vsc',\n      'react-icons/wi',\n    ]),\n  ]\n\n  if (!result.htmlLimitedBots) {\n    // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n    result.htmlLimitedBots = HTML_LIMITED_BOT_UA_RE_STRING\n  }\n\n  // \"use cache\" was originally implicitly enabled with the dynamicIO flag, so\n  // we transfer the value for dynamicIO to the explicit useCache flag to ensure\n  // backwards compatibility.\n  if (result.experimental.useCache === undefined) {\n    result.experimental.useCache = result.experimental.dynamicIO\n  }\n\n  // If dynamicIO is enabled, we also enable PPR.\n  if (result.experimental.dynamicIO) {\n    if (\n      userConfig.experimental?.ppr === false ||\n      userConfig.experimental?.ppr === 'incremental'\n    ) {\n      throw new Error(\n        `\\`experimental.ppr\\` can not be \\`${JSON.stringify(userConfig.experimental?.ppr)}\\` when \\`experimental.dynamicIO\\` is \\`true\\`. PPR is implicitly enabled when Dynamic IO is enabled.`\n      )\n    }\n\n    result.experimental.ppr = true\n  }\n\n  return result as NextConfigComplete\n}\n\nasync function applyModifyConfig(\n  config: NextConfigComplete,\n  phase: string,\n  silent: boolean\n): Promise<NextConfigComplete> {\n  if (\n    // TODO: should this be called for server start as\n    // adapters shouldn't be relying on \"next start\"\n    [PHASE_PRODUCTION_BUILD, PHASE_PRODUCTION_SERVER].includes(phase) &&\n    config.experimental?.adapterPath\n  ) {\n    const adapterMod = interopDefault(\n      await import(\n        pathToFileURL(require.resolve(config.experimental.adapterPath)).href\n      )\n    ) as NextAdapter\n\n    if (typeof adapterMod.modifyConfig === 'function') {\n      if (!silent) {\n        Log.info(`Applying modifyConfig from ${adapterMod.name}`)\n      }\n      config = await adapterMod.modifyConfig(config)\n    }\n  }\n  return config\n}\n\nexport default async function loadConfig(\n  phase: string,\n  dir: string,\n  {\n    customConfig,\n    rawConfig,\n    silent = true,\n    reportExperimentalFeatures,\n    reactProductionProfiling,\n    debugPrerender,\n  }: {\n    customConfig?: object | null\n    rawConfig?: boolean\n    silent?: boolean\n    reportExperimentalFeatures?: (\n      configuredExperimentalFeatures: ConfiguredExperimentalFeature[]\n    ) => void\n    reactProductionProfiling?: boolean\n    debugPrerender?: boolean\n  } = {}\n): Promise<NextConfigComplete> {\n  if (!process.env.__NEXT_PRIVATE_RENDER_WORKER) {\n    try {\n      loadWebpackHook()\n    } catch (err) {\n      // this can fail in standalone mode as the files\n      // aren't traced/included\n      if (!process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n        throw err\n      }\n    }\n  }\n\n  if (process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n    // we don't apply assignDefaults or modifyConfig here as it\n    // has already been applied\n    return JSON.parse(process.env.__NEXT_PRIVATE_STANDALONE_CONFIG)\n  }\n\n  const curLog = silent\n    ? {\n        warn: () => {},\n        info: () => {},\n        error: () => {},\n      }\n    : Log\n\n  loadEnvConfig(dir, phase === PHASE_DEVELOPMENT_SERVER, curLog)\n\n  let configFileName = 'next.config.js'\n\n  if (customConfig) {\n    return await applyModifyConfig(\n      assignDefaults(\n        dir,\n        {\n          configOrigin: 'server',\n          configFileName,\n          ...customConfig,\n        },\n        silent\n      ) as NextConfigComplete,\n      phase,\n      silent\n    )\n  }\n\n  const path = await findUp(CONFIG_FILES, { cwd: dir })\n\n  // If config file was found\n  if (path?.length) {\n    configFileName = basename(path)\n\n    let userConfigModule: any\n    try {\n      const envBefore = Object.assign({}, process.env)\n\n      // `import()` expects url-encoded strings, so the path must be properly\n      // escaped and (especially on Windows) absolute paths must pe prefixed\n      // with the `file://` protocol\n      if (process.env.__NEXT_TEST_MODE === 'jest') {\n        // dynamic import does not currently work inside of vm which\n        // jest relies on so we fall back to require for this case\n        // https://github.com/nodejs/node/issues/35889\n        userConfigModule = require(path)\n      } else if (configFileName === 'next.config.ts') {\n        userConfigModule = await transpileConfig({\n          nextConfigPath: path,\n          configFileName,\n          cwd: dir,\n        })\n      } else {\n        userConfigModule = await import(pathToFileURL(path).href)\n      }\n      const newEnv: typeof process.env = {} as any\n\n      for (const key of Object.keys(process.env)) {\n        if (envBefore[key] !== process.env[key]) {\n          newEnv[key] = process.env[key]\n        }\n      }\n      updateInitialEnv(newEnv)\n\n      if (rawConfig) {\n        return userConfigModule\n      }\n    } catch (err) {\n      // TODO: Modify docs to add cases of failing next.config.ts transformation\n      curLog.error(\n        `Failed to load ${configFileName}, see more info here https://nextjs.org/docs/messages/next-config-error`\n      )\n      throw err\n    }\n\n    const loadedConfig = Object.freeze(\n      (await normalizeConfig(\n        phase,\n        interopDefault(userConfigModule)\n      )) as NextConfig\n    )\n\n    const configuredExperimentalFeatures: ConfiguredExperimentalFeature[] = []\n\n    if (reportExperimentalFeatures && loadedConfig.experimental) {\n      for (const name of Object.keys(\n        loadedConfig.experimental\n      ) as (keyof ExperimentalConfig)[]) {\n        const value = loadedConfig.experimental[name]\n\n        if (name === 'turbo' && !process.env.TURBOPACK) {\n          // Ignore any Turbopack config if Turbopack is not enabled\n          continue\n        }\n\n        addConfiguredExperimentalFeature(\n          configuredExperimentalFeatures,\n          name,\n          value\n        )\n      }\n    }\n\n    // Clone a new userConfig each time to avoid mutating the original\n    const userConfig = cloneObject(loadedConfig) as NextConfig\n\n    if (!process.env.NEXT_MINIMAL) {\n      // We only validate the config against schema in non minimal mode\n      const { configSchema } =\n        require('./config-schema') as typeof import('./config-schema')\n      const state = configSchema.safeParse(userConfig)\n\n      if (state.success === false) {\n        // error message header\n        const messages = [`Invalid ${configFileName} options detected: `]\n\n        const [errorMessages, shouldExit] = normalizeNextConfigZodErrors(\n          state.error\n        )\n        // ident list item\n        for (const error of errorMessages) {\n          messages.push(`    ${error}`)\n        }\n\n        // error message footer\n        messages.push(\n          'See more info here: https://nextjs.org/docs/messages/invalid-next-config'\n        )\n\n        if (shouldExit) {\n          for (const message of messages) {\n            console.error(message)\n          }\n          await flushAndExit(1)\n        } else {\n          for (const message of messages) {\n            curLog.warn(message)\n          }\n        }\n      }\n    }\n\n    if (userConfig.target && userConfig.target !== 'server') {\n      throw new Error(\n        `The \"target\" property is no longer supported in ${configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/deprecated-target-config'\n      )\n    }\n\n    if (userConfig.amp?.canonicalBase) {\n      const { canonicalBase } = userConfig.amp || ({} as any)\n      userConfig.amp = userConfig.amp || {}\n      userConfig.amp.canonicalBase =\n        (canonicalBase?.endsWith('/')\n          ? canonicalBase.slice(0, -1)\n          : canonicalBase) || ''\n    }\n\n    if (reactProductionProfiling) {\n      userConfig.reactProductionProfiling = reactProductionProfiling\n    }\n\n    if (\n      userConfig.experimental?.turbo?.loaders &&\n      !userConfig.experimental?.turbo?.rules\n    ) {\n      curLog.warn(\n        'experimental.turbo.loaders is now deprecated. Please update next.config.js to use experimental.turbo.rules as soon as possible.\\n' +\n          'The new option is similar, but the key should be a glob instead of an extension.\\n' +\n          'Example: loaders: { \".mdx\": [\"mdx-loader\"] } -> rules: { \"*.mdx\": [\"mdx-loader\"] }\" }\\n' +\n          'See more info here https://nextjs.org/docs/app/api-reference/next-config-js/turbo'\n      )\n\n      const rules: Record<string, TurbopackLoaderItem[]> = {}\n      for (const [ext, loaders] of Object.entries(\n        userConfig.experimental.turbo.loaders\n      )) {\n        rules['*' + ext] = loaders as TurbopackLoaderItem[]\n      }\n\n      userConfig.experimental.turbo.rules = rules\n    }\n\n    if (userConfig.experimental?.turbo) {\n      curLog.warn(\n        'The config property `experimental.turbo` is deprecated. Move this setting to `config.turbopack` as Turbopack is now stable.'\n      )\n\n      // Merge the two configs, preferring values in `config.turbopack`.\n      userConfig.turbopack = {\n        ...userConfig.experimental.turbo,\n        ...userConfig.turbopack,\n      }\n      userConfig.experimental.turbopackMemoryLimit ??=\n        userConfig.experimental.turbo.memoryLimit\n      userConfig.experimental.turbopackMinify ??=\n        userConfig.experimental.turbo.minify\n      userConfig.experimental.turbopackTreeShaking ??=\n        userConfig.experimental.turbo.treeShaking\n      userConfig.experimental.turbopackSourceMaps ??=\n        userConfig.experimental.turbo.sourceMaps\n    }\n\n    if (userConfig.experimental?.useLightningcss) {\n      const { loadBindings } =\n        require('../build/swc') as typeof import('../build/swc')\n      const isLightningSupported = (await loadBindings())?.css?.lightning\n\n      if (!isLightningSupported) {\n        curLog.warn(\n          `experimental.useLightningcss is set, but the setting is disabled because next-swc/wasm does not support it yet.`\n        )\n        userConfig.experimental.useLightningcss = false\n      }\n    }\n\n    // serialize the regex config into string\n    if (userConfig?.htmlLimitedBots instanceof RegExp) {\n      // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n      userConfig.htmlLimitedBots = userConfig.htmlLimitedBots.source\n    }\n\n    if (\n      userConfig.experimental &&\n      userConfig.experimental.enablePrerenderSourceMaps === undefined &&\n      userConfig.experimental.dynamicIO === true\n    ) {\n      userConfig.experimental.enablePrerenderSourceMaps = true\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'enablePrerenderSourceMaps',\n        true,\n        'enabled by `experimental.dynamicIO`'\n      )\n    }\n\n    if (\n      debugPrerender &&\n      (phase === PHASE_PRODUCTION_BUILD || phase === PHASE_EXPORT)\n    ) {\n      userConfig.experimental ??= {}\n\n      setExperimentalFeatureForDebugPrerender(\n        userConfig.experimental,\n        'serverSourceMaps',\n        true,\n        reportExperimentalFeatures ? configuredExperimentalFeatures : undefined\n      )\n\n      setExperimentalFeatureForDebugPrerender(\n        userConfig.experimental,\n        process.env.TURBOPACK ? 'turbopackMinify' : 'serverMinification',\n        false,\n        reportExperimentalFeatures ? configuredExperimentalFeatures : undefined\n      )\n\n      setExperimentalFeatureForDebugPrerender(\n        userConfig.experimental,\n        'enablePrerenderSourceMaps',\n        true,\n        reportExperimentalFeatures ? configuredExperimentalFeatures : undefined\n      )\n\n      setExperimentalFeatureForDebugPrerender(\n        userConfig.experimental,\n        'prerenderEarlyExit',\n        false,\n        reportExperimentalFeatures ? configuredExperimentalFeatures : undefined\n      )\n    }\n\n    if (reportExperimentalFeatures) {\n      reportExperimentalFeatures(configuredExperimentalFeatures)\n    }\n\n    const completeConfig = assignDefaults(\n      dir,\n      {\n        configOrigin: relative(dir, path),\n        configFile: path,\n        configFileName,\n        ...userConfig,\n      },\n      silent\n    ) as NextConfigComplete\n    return await applyModifyConfig(completeConfig, phase, silent)\n  } else {\n    const configBaseName = basename(CONFIG_FILES[0], extname(CONFIG_FILES[0]))\n    const unsupportedConfig = findUp.sync(\n      [\n        `${configBaseName}.cjs`,\n        `${configBaseName}.cts`,\n        `${configBaseName}.mts`,\n        `${configBaseName}.json`,\n        `${configBaseName}.jsx`,\n        `${configBaseName}.tsx`,\n      ],\n      { cwd: dir }\n    )\n    if (unsupportedConfig?.length) {\n      throw new Error(\n        `Configuring Next.js via '${basename(\n          unsupportedConfig\n        )}' is not supported. Please replace the file with 'next.config.js', 'next.config.mjs', or 'next.config.ts'.`\n      )\n    }\n  }\n\n  // always call assignDefaults to ensure settings like\n  // reactRoot can be updated correctly even with no next.config.js\n  const completeConfig = assignDefaults(\n    dir,\n    { ...defaultConfig, configFileName },\n    silent\n  ) as NextConfigComplete\n  setHttpClientAndAgentOptions(completeConfig)\n  return await applyModifyConfig(completeConfig, phase, silent)\n}\n\nexport type ConfiguredExperimentalFeature = {\n  key: keyof ExperimentalConfig\n  value: ExperimentalConfig[keyof ExperimentalConfig]\n  reason?: string\n}\n\nexport function addConfiguredExperimentalFeature<\n  KeyType extends keyof ExperimentalConfig,\n>(\n  configuredExperimentalFeatures: ConfiguredExperimentalFeature[],\n  key: KeyType,\n  value: ExperimentalConfig[KeyType],\n  reason?: string\n) {\n  if (value !== (defaultConfig.experimental as Record<string, unknown>)[key]) {\n    configuredExperimentalFeatures.push({ key, value, reason })\n  }\n}\n\nfunction setExperimentalFeatureForDebugPrerender<\n  KeyType extends keyof ExperimentalConfig,\n>(\n  experimentalConfig: ExperimentalConfig,\n  key: KeyType,\n  value: ExperimentalConfig[KeyType],\n  configuredExperimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n) {\n  if (experimentalConfig[key] !== value) {\n    experimentalConfig[key] = value\n\n    if (configuredExperimentalFeatures) {\n      const action =\n        value === true ? 'enabled' : value === false ? 'disabled' : 'set'\n\n      const reason = `${action} by \\`--debug-prerender\\``\n\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        key,\n        value,\n        reason\n      )\n    }\n  }\n}\n\nfunction cloneObject(obj: any): any {\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n\n  if (Array.isArray(obj)) {\n    return obj.map(cloneObject)\n  }\n  const keys = Object.keys(obj)\n  if (keys.length === 0) {\n    return obj\n  }\n\n  return keys.reduce((acc, key) => {\n    ;(acc as any)[key] = cloneObject(obj[key])\n    return acc\n  }, {})\n}\n"], "names": ["existsSync", "basename", "extname", "join", "relative", "isAbsolute", "resolve", "pathToFileURL", "findUp", "Log", "ciEnvironment", "CONFIG_FILES", "PHASE_DEVELOPMENT_SERVER", "PHASE_EXPORT", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "defaultConfig", "normalizeConfig", "loadWebpackHook", "imageConfigDefault", "loadEnvConfig", "updateInitialEnv", "flushAndExit", "findRootDir", "setHttpClientAndAgentOptions", "pathHasPrefix", "matchRemotePattern", "hasNextSupport", "transpileConfig", "dset", "normalizeZodErrors", "HTML_LIMITED_BOT_UA_RE_STRING", "findDir", "CanaryOnlyError", "isStableBuild", "interopDefault", "normalizeNextConfigZodErrors", "error", "shouldExit", "issues", "flatMap", "issue", "message", "path", "warnOptionHasBeenDeprecated", "config", "nested<PERSON><PERSON><PERSON><PERSON><PERSON>", "reason", "silent", "hasWarned", "current", "found", "nestedPropertyKeys", "split", "key", "undefined", "warnOnce", "warnOptionHasBeenMovedOutOfExperimental", "oldExperimentalKey", "new<PERSON>ey", "configFileName", "experimental", "warn", "newKeys", "length", "shift", "warnCustomizedOption", "defaultValue", "customMessage", "segs", "seg", "assignDefaults", "dir", "userConfig", "result", "exportTrailingSlash", "trailingSlash", "Object", "keys", "reduce", "currentConfig", "value", "Error", "userDistDir", "trim", "Array", "isArray", "for<PERSON>ach", "ext", "constructor", "c", "k", "v", "ppr", "dynamicIO", "trustHostHeader", "allowDevelopmentBuild", "process", "env", "NODE_ENV", "feature", "turbopackPersistentCaching", "nodeMiddleware", "output", "i18n", "rewrites", "redirects", "headers", "assetPrefix", "basePath", "startsWith", "endsWith", "amp", "canonicalBase", "images", "localPatterns", "hasMatch", "some", "pattern", "pathname", "search", "push", "remotePatterns", "map", "protocol", "hostname", "port", "proto", "replace", "includes", "url", "URL", "hasMatchForAssetPrefix", "domains", "loader", "loaderFile", "absolutePath", "devtoolNewPanelUI", "devtoolSegmentExplorer", "hasWarnedBuildActivityPosition", "devIndicators", "buildActivityPosition", "position", "outputStandalone", "serverActions", "bodySizeLimit", "parseInt", "toString", "isNaN", "outputFileTracingRoot", "turbopack", "root", "NEXT_DEPLOYMENT_ID", "deploymentId", "rootDir", "hasAppDir", "Boolean", "i18nType", "locales", "defaultLocaleType", "defaultLocale", "invalidDomainItems", "filter", "item", "domain", "console", "defaultLocaleDuplicate", "find", "altItem", "hasInvalidLocale", "locale", "domainItem", "JSON", "stringify", "invalidLocales", "String", "normalizedLocales", "Set", "duplicateLocales", "localeLower", "toLowerCase", "has", "add", "size", "localeDetectionType", "localeDetection", "<PERSON><PERSON><PERSON><PERSON>", "cacheLife", "defaultDefault", "revalidate", "expire", "staleTimes", "static", "defaultCacheLifeProfile", "stale", "staticStaleTime", "expireTime", "secondsCacheLifeProfile", "dynamicStaleTime", "dynamic", "cacheHandlers", "allowedHandlerNameRegex", "handler<PERSON>eys", "invalidHandlerItems", "test", "handler<PERSON><PERSON>", "userProvidedModularizeImports", "modularizeImports", "transform", "lodash", "userProvidedOptimizePackageImports", "optimizePackageImports", "htmlLimitedBots", "useCache", "applyModifyConfig", "phase", "adapterPath", "adapterMod", "require", "href", "modifyConfig", "info", "name", "loadConfig", "customConfig", "rawConfig", "reportExperimentalFeatures", "reactProductionProfiling", "debugPrerender", "__NEXT_PRIVATE_RENDER_WORKER", "err", "__NEXT_PRIVATE_STANDALONE_CONFIG", "parse", "curLog", "config<PERSON><PERSON><PERSON>", "cwd", "userConfigModule", "envBefore", "assign", "__NEXT_TEST_MODE", "nextConfigPath", "newEnv", "loadedConfig", "freeze", "configuredExperimentalFeatures", "TURBOPACK", "addConfiguredExperimentalFeature", "cloneObject", "NEXT_MINIMAL", "configSchema", "state", "safeParse", "success", "messages", "errorMessages", "target", "slice", "turbo", "loaders", "rules", "entries", "turbopackMemoryLimit", "memoryLimit", "turbopackMinify", "minify", "turbopackTreeShaking", "treeShaking", "turbopackSourceMaps", "sourceMaps", "useLightningcss", "loadBindings", "isLightningSupported", "css", "lightning", "RegExp", "source", "enablePrerenderSourceMaps", "setExperimentalFeatureForDebugPrerender", "completeConfig", "configFile", "configBaseName", "unsupportedConfig", "sync", "experimentalConfig", "action", "obj", "acc"], "mappings": "AAAA,SAASA,UAAU,QAAQ,KAAI;AAC/B,SAASC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAM;AAC7E,SAASC,aAAa,QAAQ,MAAK;AACnC,OAAOC,YAAY,6BAA4B;AAC/C,YAAYC,SAAS,sBAAqB;AAC1C,YAAYC,mBAAmB,oBAAmB;AAClD,SACEC,YAAY,EACZC,wBAAwB,EACxBC,YAAY,EACZC,sBAAsB,EACtBC,uBAAuB,QAClB,0BAAyB;AAChC,SAASC,aAAa,EAAEC,eAAe,QAAQ,kBAAiB;AAShE,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,kBAAkB,QAAQ,6BAA4B;AAE/D,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,YAAW;AAC3D,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,4BAA4B,QAAQ,yBAAwB;AACrE,SAASC,aAAa,QAAQ,6CAA4C;AAC1E,SAASC,kBAAkB,QAAQ,qCAAoC;AAGvE,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,IAAI,QAAQ,qBAAoB;AACzC,SAASC,kBAAkB,QAAQ,oBAAmB;AACtD,SAASC,6BAA6B,QAAQ,oCAAmC;AACjF,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,eAAe,EAAEC,aAAa,QAAQ,4BAA2B;AAC1E,SAASC,cAAc,QAAQ,yBAAwB;AAEvD,SAASlB,eAAe,QAAQ,kBAAiB;AAGjD,SAASmB,6BACPC,KAA2B;IAE3B,IAAIC,aAAa;IACjB,MAAMC,SAAST,mBAAmBO;IAClC,OAAO;QACLE,OAAOC,OAAO,CAAC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAE;YAChC,IAAID,MAAME,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC9B,oEAAoE;gBACpEL,aAAa;YACf;YAEA,OAAOI;QACT;QACAJ;KACD;AACH;AAEA,OAAO,SAASM,4BACdC,MAAkB,EAClBC,iBAAyB,EACzBC,MAAc,EACdC,MAAe;IAEf,IAAIC,YAAY;IAChB,IAAI,CAACD,QAAQ;QACX,IAAIE,UAAUL;QACd,IAAIM,QAAQ;QACZ,MAAMC,qBAAqBN,kBAAkBO,KAAK,CAAC;QACnD,KAAK,MAAMC,OAAOF,mBAAoB;YACpC,IAAIF,OAAO,CAACI,IAAI,KAAKC,WAAW;gBAC9BL,UAAUA,OAAO,CAACI,IAAI;YACxB,OAAO;gBACLH,QAAQ;gBACR;YACF;QACF;QACA,IAAIA,OAAO;YACT1C,IAAI+C,QAAQ,CAACT;YACbE,YAAY;QACd;IACF;IACA,OAAOA;AACT;AAEA,OAAO,SAASQ,wCACdZ,MAAkB,EAClBa,kBAA0B,EAC1BC,MAAc,EACdC,cAAsB,EACtBZ,MAAe;IAEf,IAAIH,OAAOgB,YAAY,IAAIH,sBAAsBb,OAAOgB,YAAY,EAAE;QACpE,IAAI,CAACb,QAAQ;YACXvC,IAAIqD,IAAI,CACN,CAAC,eAAe,EAAEJ,mBAAmB,uBAAuB,EAAEC,OAAO,IAAI,CAAC,GACxE,CAAC,mBAAmB,EAAEC,eAAe,kBAAkB,CAAC;QAE9D;QAEA,IAAIV,UAAUL;QACd,MAAMkB,UAAUJ,OAAON,KAAK,CAAC;QAC7B,MAAOU,QAAQC,MAAM,GAAG,EAAG;YACzB,MAAMV,MAAMS,QAAQE,KAAK;YACzBf,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAI,CAAC;YAChCJ,UAAUA,OAAO,CAACI,IAAI;QACxB;QACAJ,OAAO,CAACa,QAAQE,KAAK,GAAI,GAAG,AAACpB,OAAOgB,YAAY,AAAQ,CAACH,mBAAmB;IAC9E;IAEA,OAAOb;AACT;AAEA,SAASqB,qBACPrB,MAAkB,EAClBS,GAAW,EACXa,YAAiB,EACjBC,aAAqB,EACrBR,cAAsB,EACtBZ,MAAe;IAEf,MAAMqB,OAAOf,IAAID,KAAK,CAAC;IACvB,IAAIH,UAAUL;IAEd,MAAOwB,KAAKL,MAAM,IAAI,EAAG;QACvB,MAAMM,MAAMD,KAAKJ,KAAK;QACtB,IAAI,CAAEK,CAAAA,OAAOpB,OAAM,GAAI;YACrB;QACF;QACAA,UAAUA,OAAO,CAACoB,IAAI;IACxB;IAEA,IAAI,CAACtB,UAAUE,YAAYiB,cAAc;QACvC1D,IAAIqD,IAAI,CACN,CAAC,KAAK,EAAER,IAAI,4BAA4B,EAAEc,gBAAgBA,gBAAgB,OAAO,GAAG,+BAA+B,EAAER,eAAe,CAAC,CAAC;IAE1I;AACF;AAEA,SAASW,eACPC,GAAW,EACXC,UAAmD,EACnDzB,MAAe;QAkGXhC,6BAMAA,8BAgBC0D,sBAKHA,uBAwXOA,oCAAAA,uBA8CLA,mBAckCA,oBAKCA,oBA2LDA,uBA+DlCA,uBAuDFA;IAr2BF,MAAMd,iBAAiBa,WAAWb,cAAc;IAChD,IAAI,OAAOa,WAAWE,mBAAmB,KAAK,aAAa;QACzD,IAAI,CAAC3B,QAAQ;YACXvC,IAAIqD,IAAI,CACN,CAAC,yFAAyF,EAAEF,eAAe,CAAC,CAAC;QAEjH;QACA,IAAI,OAAOa,WAAWG,aAAa,KAAK,aAAa;YACnDH,WAAWG,aAAa,GAAGH,WAAWE,mBAAmB;QAC3D;QACA,OAAOF,WAAWE,mBAAmB;IACvC;IAEA,MAAM9B,SAASgC,OAAOC,IAAI,CAACL,YAAYM,MAAM,CAC3C,CAACC,eAAe1B;QACd,MAAM2B,QAAQR,UAAU,CAACnB,IAAI;QAE7B,IAAI2B,UAAU1B,aAAa0B,UAAU,MAAM;YACzC,OAAOD;QACT;QAEA,IAAI1B,QAAQ,WAAW;YACrB,IAAI,OAAO2B,UAAU,UAAU;gBAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,+CAA+C,EAAE,OAAOD,MAAM,CAAC,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAME,cAAcF,MAAMG,IAAI;YAE9B,qEAAqE;YACrE,eAAe;YACf,IAAID,gBAAgB,UAAU;gBAC5B,MAAM,qBAEL,CAFK,IAAID,MACR,CAAC,4IAA4I,CAAC,GAD1I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,2EAA2E;YAC3E,8CAA8C;YAC9C,IAAIC,YAAYnB,MAAM,KAAK,GAAG;gBAC5B,MAAM,qBAEL,CAFK,IAAIkB,MACR,CAAC,8GAA8G,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAI5B,QAAQ,kBAAkB;YAC5B,IAAI,CAAC+B,MAAMC,OAAO,CAACL,QAAQ;gBACzB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,4DAA4D,EAAED,MAAM,0CAA0C,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI,CAACA,MAAMjB,MAAM,EAAE;gBACjB,MAAM,qBAEL,CAFK,IAAIkB,MACR,CAAC,uGAAuG,CAAC,GADrG,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAD,MAAMM,OAAO,CAAC,CAACC;gBACb,IAAI,OAAOA,QAAQ,UAAU;oBAC3B,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,4DAA4D,EAAEM,IAAI,WAAW,EAAE,OAAOA,IAAI,0CAA0C,CAAC,GADlI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,MAAMrB,eAAe,AAACnD,aAAyC,CAACsC,IAAI;QAEpE,IACE,CAAC,CAAC2B,SACFA,MAAMQ,WAAW,KAAKZ,UACtB,OAAOV,iBAAiB,UACxB;YACAa,aAAa,CAAC1B,IAAI,GAAG;gBACnB,GAAGa,YAAY;gBACf,GAAGU,OAAOC,IAAI,CAACG,OAAOF,MAAM,CAAM,CAACW,GAAGC;oBACpC,MAAMC,IAAIX,KAAK,CAACU,EAAE;oBAClB,IAAIC,MAAMrC,aAAaqC,MAAM,MAAM;wBACjCF,CAAC,CAACC,EAAE,GAAGC;oBACT;oBACA,OAAOF;gBACT,GAAG,CAAC,EAAE;YACR;QACF,OAAO;YACLV,aAAa,CAAC1B,IAAI,GAAG2B;QACvB;QAEA,OAAOD;IACT,GACA,CAAC;IAGH,iDAAiD;IACjD,uEAAuE;IACvE,6CAA6C;IAC7C,KAAIhE,8BAAAA,cAAc6C,YAAY,qBAA1B7C,4BAA4B6E,GAAG,EAAE;QACnCpF,IAAIqD,IAAI,CACN,CAAC,2HAA2H,CAAC;IAEjI;IAEA,KAAI9C,+BAAAA,cAAc6C,YAAY,qBAA1B7C,6BAA4B8E,SAAS,EAAE;QACzCrF,IAAIqD,IAAI,CACN,CAAC,8IAA8I,CAAC;IAEpJ;IAEA,MAAMY,SAAS;QACb,GAAG1D,aAAa;QAChB,GAAG6B,MAAM;QACTgB,cAAc;YACZ,GAAG7C,cAAc6C,YAAY;YAC7B,GAAGhB,OAAOgB,YAAY;QACxB;IACF;IAEA,qEAAqE;IACrE,IAAI,GAACa,uBAAAA,OAAOb,YAAY,qBAAnBa,qBAAqBqB,eAAe,KAAIrF,cAAciB,cAAc,EAAE;QACzE+C,OAAOb,YAAY,CAACkC,eAAe,GAAG;IACxC;IAEA,IACErB,EAAAA,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqBsB,qBAAqB,KAC1CC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzB;QACA,MAAM,qBAEL,CAFK,IAAIjB,MACR,CAAC,sGAAsG,CAAC,GADpG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIhD,iBAAiB;YAEfwC,uBAEOA,uBAEAA,uBAIAA;QATX,oEAAoE;QACpE,KAAIA,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqBmB,GAAG,EAAE;YAC5B,MAAM,qBAAoD,CAApD,IAAI5D,gBAAgB;gBAAEmE,SAAS;YAAmB,IAAlD,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D,OAAO,KAAI1B,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqBoB,SAAS,EAAE;YACzC,MAAM,qBAA0D,CAA1D,IAAI7D,gBAAgB;gBAAEmE,SAAS;YAAyB,IAAxD,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE,OAAO,KAAI1B,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqB2B,0BAA0B,EAAE;YAC1D,MAAM,qBAEJ,CAFI,IAAIpE,gBAAgB;gBACxBmE,SAAS;YACX,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH,OAAO,KAAI1B,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqB4B,cAAc,EAAE;YAC9C,MAAM,qBAA+D,CAA/D,IAAIrE,gBAAgB;gBAAEmE,SAAS;YAA8B,IAA7D,qBAAA;uBAAA;4BAAA;8BAAA;YAA8D;QACtE;IACF;IAEA,IAAI1B,OAAO6B,MAAM,KAAK,UAAU;QAC9B,IAAI7B,OAAO8B,IAAI,EAAE;YACf,MAAM,qBAEL,CAFK,IAAItB,MACR,+HADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACvD,gBAAgB;YACnB,IAAI+C,OAAO+B,QAAQ,EAAE;gBACnBhG,IAAIqD,IAAI,CACN;YAEJ;YACA,IAAIY,OAAOgC,SAAS,EAAE;gBACpBjG,IAAIqD,IAAI,CACN;YAEJ;YACA,IAAIY,OAAOiC,OAAO,EAAE;gBAClBlG,IAAIqD,IAAI,CACN;YAEJ;QACF;IACF;IAEA,IAAI,OAAOY,OAAOkC,WAAW,KAAK,UAAU;QAC1C,MAAM,qBAEL,CAFK,IAAI1B,MACR,CAAC,mDAAmD,EAAE,OAAOR,OAAOkC,WAAW,CAAC,sDAAsD,CAAC,GADnI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,OAAOlC,OAAOmC,QAAQ,KAAK,UAAU;QACvC,MAAM,qBAEL,CAFK,IAAI3B,MACR,CAAC,gDAAgD,EAAE,OAAOR,OAAOmC,QAAQ,CAAC,CAAC,CAAC,GADxE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAInC,OAAOmC,QAAQ,KAAK,IAAI;QAC1B,IAAInC,OAAOmC,QAAQ,KAAK,KAAK;YAC3B,MAAM,qBAEL,CAFK,IAAI3B,MACR,CAAC,iFAAiF,CAAC,GAD/E,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACR,OAAOmC,QAAQ,CAACC,UAAU,CAAC,MAAM;YACpC,MAAM,qBAEL,CAFK,IAAI5B,MACR,CAAC,iDAAiD,EAAER,OAAOmC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAInC,OAAOmC,QAAQ,KAAK,KAAK;gBAWvBnC;YAVJ,IAAIA,OAAOmC,QAAQ,CAACE,QAAQ,CAAC,MAAM;gBACjC,MAAM,qBAEL,CAFK,IAAI7B,MACR,CAAC,iDAAiD,EAAER,OAAOmC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAInC,OAAOkC,WAAW,KAAK,IAAI;gBAC7BlC,OAAOkC,WAAW,GAAGlC,OAAOmC,QAAQ;YACtC;YAEA,IAAInC,EAAAA,cAAAA,OAAOsC,GAAG,qBAAVtC,YAAYuC,aAAa,MAAK,IAAI;gBACpCvC,OAAOsC,GAAG,CAACC,aAAa,GAAGvC,OAAOmC,QAAQ;YAC5C;QACF;IACF;IAEA,IAAInC,0BAAAA,OAAQwC,MAAM,EAAE;QAClB,MAAMA,SAAsBxC,OAAOwC,MAAM;QAEzC,IAAI,OAAOA,WAAW,UAAU;YAC9B,MAAM,qBAEL,CAFK,IAAIhC,MACR,CAAC,8CAA8C,EAAE,OAAOgC,OAAO,6EAA6E,CAAC,GADzI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIA,OAAOC,aAAa,EAAE;YACxB,IAAI,CAAC9B,MAAMC,OAAO,CAAC4B,OAAOC,aAAa,GAAG;gBACxC,MAAM,qBAEL,CAFK,IAAIjC,MACR,CAAC,2DAA2D,EAAE,OAAOgC,OAAOC,aAAa,CAAC,6EAA6E,CAAC,GADpK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,6DAA6D;YAC7D,MAAMC,WAAWF,OAAOC,aAAa,CAACE,IAAI,CACxC,CAACC,UACCA,QAAQC,QAAQ,KAAK,4BAA4BD,QAAQE,MAAM,KAAK;YAExE,IAAI,CAACJ,UAAU;gBACb,iDAAiD;gBACjDF,OAAOC,aAAa,CAACM,IAAI,CAAC;oBACxBF,UAAU;oBACVC,QAAQ;gBACV;YACF;QACF;QAEA,IAAIN,OAAOQ,cAAc,EAAE;gBA+BrB7E;YA9BJ,IAAI,CAACwC,MAAMC,OAAO,CAAC4B,OAAOQ,cAAc,GAAG;gBACzC,MAAM,qBAEL,CAFK,IAAIxC,MACR,CAAC,4DAA4D,EAAE,OAAOgC,OAAOQ,cAAc,CAAC,6EAA6E,CAAC,GADtK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,6EAA6E;YAC7E,4EAA4E;YAC5E,0DAA0D;YAC1DR,OAAOQ,cAAc,GAAGR,OAAOQ,cAAc,CAACC,GAAG,CAC/C,CAAC,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEP,QAAQ,EAAEC,MAAM,EAAE;gBAC7C,MAAMO,QAAQH,4BAAAA,SAAUI,OAAO,CAAC,MAAM;gBACtC,IAAI,CAAC;oBAAC;oBAAQ;oBAASzE;iBAAU,CAAC0E,QAAQ,CAACF,QAAQ;oBACjD,MAAM,qBAEL,CAFK,IAAI7C,MACR,CAAC,+EAA+E,EAAE6C,MAAM,EAAE,CAAC,GADvF,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,OAAO;oBACLH,UAAUG;oBACVF;oBACAC;oBACAP;oBACAC;gBACF;YACF;YAGF,4DAA4D;YAC5D,2DAA2D;YAC3D,gBAAgB;YAChB,KAAI3E,sBAAAA,OAAO+D,WAAW,qBAAlB/D,oBAAoBiE,UAAU,CAAC,SAAS;gBAC1C,IAAI;oBACF,MAAMoB,MAAM,IAAIC,IAAItF,OAAO+D,WAAW;oBACtC,MAAMwB,yBAAyBlB,OAAOQ,cAAc,CAACL,IAAI,CAAC,CAACC,UACzD5F,mBAAmB4F,SAASY;oBAG9B,qEAAqE;oBACrE,IAAI,CAACE,wBAAwB;wBAC3BlB,OAAOQ,cAAc,CAACD,IAAI,CAAC;4BACzBI,UAAUK,IAAIL,QAAQ;4BACtBD,UAAUM,IAAIN,QAAQ,CAACI,OAAO,CAAC,MAAM;4BACrCF,MAAMI,IAAIJ,IAAI;wBAChB;oBACF;gBACF,EAAE,OAAOzF,OAAO;oBACd,MAAM,qBAEL,CAFK,IAAI6C,MACR,CAAC,8CAA8C,EAAE7C,OAAO,GADpD,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,IAAI6E,OAAOmB,OAAO,EAAE;YAClB,IAAI,CAAChD,MAAMC,OAAO,CAAC4B,OAAOmB,OAAO,GAAG;gBAClC,MAAM,qBAEL,CAFK,IAAInD,MACR,CAAC,qDAAqD,EAAE,OAAOgC,OAAOmB,OAAO,CAAC,6EAA6E,CAAC,GADxJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAI,CAACnB,OAAOoB,MAAM,EAAE;YAClBpB,OAAOoB,MAAM,GAAG;QAClB;QAEA,IACEpB,OAAOoB,MAAM,KAAK,aAClBpB,OAAOoB,MAAM,KAAK,YAClBpB,OAAOvE,IAAI,KAAKxB,mBAAmBwB,IAAI,EACvC;YACA,MAAM,qBAEL,CAFK,IAAIuC,MACR,CAAC,kCAAkC,EAAEgC,OAAOoB,MAAM,CAAC,sKAAsK,CAAC,GADtN,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IACEpB,OAAOvE,IAAI,KAAKxB,mBAAmBwB,IAAI,IACvC+B,OAAOmC,QAAQ,IACf,CAACpF,cAAcyF,OAAOvE,IAAI,EAAE+B,OAAOmC,QAAQ,GAC3C;YACAK,OAAOvE,IAAI,GAAG,GAAG+B,OAAOmC,QAAQ,GAAGK,OAAOvE,IAAI,EAAE;QAClD;QAEA,8EAA8E;QAC9E,IACEuE,OAAOvE,IAAI,IACX,CAACuE,OAAOvE,IAAI,CAACoE,QAAQ,CAAC,QACrBG,CAAAA,OAAOoB,MAAM,KAAK,aAAa5D,OAAOE,aAAa,AAAD,GACnD;YACAsC,OAAOvE,IAAI,IAAI;QACjB;QAEA,IAAIuE,OAAOqB,UAAU,EAAE;YACrB,IAAIrB,OAAOoB,MAAM,KAAK,aAAapB,OAAOoB,MAAM,KAAK,UAAU;gBAC7D,MAAM,qBAEL,CAFK,IAAIpD,MACR,CAAC,kCAAkC,EAAEgC,OAAOoB,MAAM,CAAC,uFAAuF,CAAC,GADvI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAME,eAAerI,KAAKqE,KAAK0C,OAAOqB,UAAU;YAChD,IAAI,CAACvI,WAAWwI,eAAe;gBAC7B,MAAM,qBAEL,CAFK,IAAItD,MACR,CAAC,+CAA+C,EAAEsD,aAAa,EAAE,CAAC,GAD9D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAtB,OAAOqB,UAAU,GAAGC;QACtB;IACF;IAEA,IAAI9D,OAAOb,YAAY,CAAC4E,iBAAiB,EAAE;QACzC/D,OAAOb,YAAY,CAAC6E,sBAAsB,GAAG;IAC/C;IAEAxE,qBACEQ,QACA,6BACA,MACA,mGACAd,gBACAZ;IAGFJ,4BACE8B,QACA,oCACA,CAAC,yIAAyI,EAAEd,eAAe,CAAC,CAAC,EAC7JZ;IAGFJ,4BACE8B,QACA,sBACA,CAAC,8GAA8G,EAAEd,eAAe,CAAC,CAAC,EAClIZ;IAGFJ,4BACE8B,QACA,8BACA,CAAC,+FAA+F,EAAEd,eAAe,CAAC,CAAC,EACnHZ;IAGFJ,4BACE8B,QACA,+BACA,CAAC,gGAAgG,EAAEd,eAAe,CAAC,CAAC,EACpHZ;IAGF,MAAM2F,iCAAiC/F,4BACrC8B,QACA,uCACA,CAAC,2GAA2G,EAAEd,eAAe,kBAAkB,CAAC,EAChJZ;IAEF,IACE2F,kCACAjE,OAAOkE,aAAa,KAAK,SACzB,2BAA2BlE,OAAOkE,aAAa,IAC/ClE,OAAOkE,aAAa,CAACC,qBAAqB,KAAKnE,OAAOkE,aAAa,CAACE,QAAQ,EAC5E;QACArI,IAAI+C,QAAQ,CACV,CAAC,yDAAyD,EAAEkB,OAAOkE,aAAa,CAACC,qBAAqB,CAAC,iCAAiC,EAAEnE,OAAOkE,aAAa,CAACE,QAAQ,CAAC,sCAAsC,EAAEpE,OAAOkE,aAAa,CAACC,qBAAqB,CAAC,8BAA8B,CAAC;QAE5RnE,OAAOkE,aAAa,CAACE,QAAQ,GAAGpE,OAAOkE,aAAa,CAACC,qBAAqB;IAC5E;IAEApF,wCACEiB,QACA,wBACA,iCACAd,gBACAZ;IAEFS,wCACEiB,QACA,oCACA,0BACAd,gBACAZ;IAEFS,wCACEiB,QACA,SACA,kBACAd,gBACAZ;IAEFS,wCACEiB,QACA,oBACA,6BACAd,gBACAZ;IAEFS,wCACEiB,QACA,WACA,oBACAd,gBACAZ;IAEFS,wCACEiB,QACA,yBACA,kCACAd,gBACAZ;IAEFS,wCACEiB,QACA,iBACA,0BACAd,gBACAZ;IAEFS,wCACEiB,QACA,YACA,cACAd,gBACAZ;IAEFS,wCACEiB,QACA,yBACA,yBACAd,gBACAZ;IAEFS,wCACEiB,QACA,6BACA,6BACAd,gBACAZ;IAEFS,wCACEiB,QACA,6BACA,6BACAd,gBACAZ;IAGF,IAAI,AAAC0B,OAAOb,YAAY,CAASkF,gBAAgB,EAAE;QACjD,IAAI,CAAC/F,QAAQ;YACXvC,IAAIqD,IAAI,CACN,CAAC,iGAAiG,CAAC;QAEvG;QACAY,OAAO6B,MAAM,GAAG;IAClB;IAEA,IACE,SAAO7B,wBAAAA,OAAOb,YAAY,sBAAnBa,qCAAAA,sBAAqBsE,aAAa,qBAAlCtE,mCAAoCuE,aAAa,MAAK,aAC7D;YAEEvE;QADF,MAAMO,QAAQiE,UACZxE,sCAAAA,OAAOb,YAAY,CAACmF,aAAa,qBAAjCtE,oCAAmCuE,aAAa,CAACE,QAAQ;QAE3D,IAAIC,MAAMnE,UAAUA,QAAQ,GAAG;YAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,8KADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEAzB,wCACEiB,QACA,qBACA,qBACAd,gBACAZ;IAEFS,wCACEiB,QACA,8BACA,8BACAd,gBACAZ;IAEFS,wCACEiB,QACA,6BACA,6BACAd,gBACAZ;IAGF,IACE0B,CAAAA,0BAAAA,OAAQ2E,qBAAqB,KAC7B,CAAChJ,WAAWqE,OAAO2E,qBAAqB,GACxC;QACA3E,OAAO2E,qBAAqB,GAAG/I,QAAQoE,OAAO2E,qBAAqB;QACnE,IAAI,CAACrG,QAAQ;YACXvC,IAAIqD,IAAI,CACN,CAAC,iDAAiD,EAAEY,OAAO2E,qBAAqB,EAAE;QAEtF;IACF;IAEA,IAAI3E,CAAAA,2BAAAA,oBAAAA,OAAQ4E,SAAS,qBAAjB5E,kBAAmB6E,IAAI,KAAI,CAAClJ,WAAWqE,OAAO4E,SAAS,CAACC,IAAI,GAAG;QACjE7E,OAAO4E,SAAS,CAACC,IAAI,GAAGjJ,QAAQoE,OAAO4E,SAAS,CAACC,IAAI;QACrD,IAAI,CAACvG,QAAQ;YACXvC,IAAIqD,IAAI,CACN,CAAC,0CAA0C,EAAEY,OAAO4E,SAAS,CAACC,IAAI,EAAE;QAExE;IACF;IAEA,6BAA6B;IAC7B,IAAItD,QAAQC,GAAG,CAACsD,kBAAkB,EAAE;QAClC9E,OAAO+E,YAAY,GAAGxD,QAAQC,GAAG,CAACsD,kBAAkB;IACtD;IAEA,IAAI9E,CAAAA,0BAAAA,OAAQ2E,qBAAqB,KAAI,EAAC3E,2BAAAA,qBAAAA,OAAQ4E,SAAS,qBAAjB5E,mBAAmB6E,IAAI,GAAE;QAC7D1H,KAAK6C,QAAQ;YAAC;YAAa;SAAO,EAAEA,OAAO2E,qBAAqB;IAClE;IAEA,iDAAiD;IACjD,IAAI,EAAC3E,0BAAAA,OAAQ2E,qBAAqB,KAAI,EAAC3E,2BAAAA,qBAAAA,OAAQ4E,SAAS,qBAAjB5E,mBAAmB6E,IAAI,GAAE;QAC9D,IAAIG,UAAUnI,YAAYiD;QAE1B,IAAIkF,SAAS;gBAKNhF;YAJL,IAAI,EAACA,0BAAAA,OAAQ2E,qBAAqB,GAAE;gBAClC3E,OAAO2E,qBAAqB,GAAGK;YACjC;YAEA,IAAI,EAAChF,2BAAAA,qBAAAA,OAAQ4E,SAAS,qBAAjB5E,mBAAmB6E,IAAI,GAAE;gBAC5B1H,KAAK6C,QAAQ;oBAAC;oBAAa;iBAAO,EAAEgF;YACtC;QACF;IACF;IAEAlI,6BAA6BkD,UAAU1D;IAEvC,IAAI0D,OAAO8B,IAAI,EAAE;QACf,MAAMmD,YAAYC,QAAQ5H,QAAQwC,KAAK;QAEvC,IAAImF,WAAW;YACb/G,4BACE8B,QACA,QACA,CAAC,sBAAsB,EAAEd,eAAe,uKAAuK,CAAC,EAChNZ;QAEJ;QAEA,MAAM,EAAEwD,IAAI,EAAE,GAAG9B;QACjB,MAAMmF,WAAW,OAAOrD;QAExB,IAAIqD,aAAa,UAAU;YACzB,MAAM,qBAEL,CAFK,IAAI3E,MACR,CAAC,4CAA4C,EAAE2E,SAAS,2EAA2E,CAAC,GADhI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACxE,MAAMC,OAAO,CAACkB,KAAKsD,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAI5E,MACR,CAAC,mDAAmD,EAAE,OAAOsB,KAAKsD,OAAO,CAAC,2EAA2E,CAAC,GADlJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAItD,KAAKsD,OAAO,CAAC9F,MAAM,GAAG,OAAO,CAAChB,QAAQ;YACxCvC,IAAIqD,IAAI,CACN,CAAC,SAAS,EAAE0C,KAAKsD,OAAO,CAAC9F,MAAM,CAAC,mLAAmL,CAAC;QAExN;QAEA,MAAM+F,oBAAoB,OAAOvD,KAAKwD,aAAa;QAEnD,IAAI,CAACxD,KAAKwD,aAAa,IAAID,sBAAsB,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAI7E,MACR,CAAC,0HAA0H,CAAC,GADxH,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,OAAOsB,KAAK6B,OAAO,KAAK,eAAe,CAAChD,MAAMC,OAAO,CAACkB,KAAK6B,OAAO,GAAG;YACvE,MAAM,qBAEL,CAFK,IAAInD,MACR,CAAC,2IAA2I,EAAE,OAAOsB,KAAK6B,OAAO,CAAC,2EAA2E,CAAC,GAD1O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI7B,KAAK6B,OAAO,EAAE;YAChB,MAAM4B,qBAAqBzD,KAAK6B,OAAO,CAAC6B,MAAM,CAAC,CAACC;oBAYf3D;gBAX/B,IAAI,CAAC2D,QAAQ,OAAOA,SAAS,UAAU,OAAO;gBAC9C,IAAI,CAACA,KAAKH,aAAa,EAAE,OAAO;gBAChC,IAAI,CAACG,KAAKC,MAAM,IAAI,OAAOD,KAAKC,MAAM,KAAK,UAAU,OAAO;gBAE5D,IAAID,KAAKC,MAAM,CAACnC,QAAQ,CAAC,MAAM;oBAC7BoC,QAAQvG,IAAI,CACV,CAAC,cAAc,EAAEqG,KAAKC,MAAM,CAAC,2GAA2G,CAAC;oBAE3I,OAAO;gBACT;gBAEA,MAAME,0BAAyB9D,gBAAAA,KAAK6B,OAAO,qBAAZ7B,cAAc+D,IAAI,CAC/C,CAACC,UACCA,QAAQR,aAAa,KAAKG,KAAKH,aAAa,IAC5CQ,QAAQJ,MAAM,KAAKD,KAAKC,MAAM;gBAGlC,IAAI,CAACpH,UAAUsH,wBAAwB;oBACrCD,QAAQvG,IAAI,CACV,CAAC,KAAK,EAAEqG,KAAKC,MAAM,CAAC,KAAK,EAAEE,uBAAuBF,MAAM,CAAC,8BAA8B,EAAED,KAAKH,aAAa,CAAC,+DAA+D,CAAC;oBAE9K,OAAO;gBACT;gBAEA,IAAIS,mBAAmB;gBAEvB,IAAIpF,MAAMC,OAAO,CAAC6E,KAAKL,OAAO,GAAG;oBAC/B,KAAK,MAAMY,UAAUP,KAAKL,OAAO,CAAE;wBACjC,IAAI,OAAOY,WAAW,UAAUD,mBAAmB;wBAEnD,KAAK,MAAME,cAAcnE,KAAK6B,OAAO,IAAI,EAAE,CAAE;4BAC3C,IAAIsC,eAAeR,MAAM;4BACzB,IAAIQ,WAAWb,OAAO,IAAIa,WAAWb,OAAO,CAAC7B,QAAQ,CAACyC,SAAS;gCAC7DL,QAAQvG,IAAI,CACV,CAAC,KAAK,EAAEqG,KAAKC,MAAM,CAAC,KAAK,EAAEO,WAAWP,MAAM,CAAC,wBAAwB,EAAEM,OAAO,sEAAsE,CAAC;gCAEvJD,mBAAmB;gCACnB;4BACF;wBACF;oBACF;gBACF;gBAEA,OAAOA;YACT;YAEA,IAAIR,mBAAmBjG,MAAM,GAAG,GAAG;gBACjC,MAAM,qBAML,CANK,IAAIkB,MACR,CAAC,8BAA8B,EAAE+E,mBAC9BtC,GAAG,CAAC,CAACwC,OAAcS,KAAKC,SAAS,CAACV,OAClChK,IAAI,CACH,MACA,8KAA8K,CAAC,GAL/K,qBAAA;2BAAA;gCAAA;kCAAA;gBAMN;YACF;QACF;QAEA,IAAI,CAACkF,MAAMC,OAAO,CAACkB,KAAKsD,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAI5E,MACR,CAAC,2FAA2F,EAAE,OAAOsB,KAAKsD,OAAO,CAAC,2EAA2E,CAAC,GAD1L,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMgB,iBAAiBtE,KAAKsD,OAAO,CAACI,MAAM,CACxC,CAACQ,SAAgB,OAAOA,WAAW;QAGrC,IAAII,eAAe9G,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAOL,CAPK,IAAIkB,MACR,CAAC,gDAAgD,EAAE4F,eAChDnD,GAAG,CAACoD,QACJ5K,IAAI,CACH,MACA,wEAAwE,CAAC,GAC3E,CAAC,+HAA+H,CAAC,GAN/H,qBAAA;uBAAA;4BAAA;8BAAA;YAON;QACF;QAEA,IAAI,CAACqG,KAAKsD,OAAO,CAAC7B,QAAQ,CAACzB,KAAKwD,aAAa,GAAG;YAC9C,MAAM,qBAEL,CAFK,IAAI9E,MACR,CAAC,0IAA0I,CAAC,GADxI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM8F,oBAAoB,IAAIC;QAC9B,MAAMC,mBAAmB,IAAID;QAE7BzE,KAAKsD,OAAO,CAACvE,OAAO,CAAC,CAACmF;YACpB,MAAMS,cAAcT,OAAOU,WAAW;YACtC,IAAIJ,kBAAkBK,GAAG,CAACF,cAAc;gBACtCD,iBAAiBI,GAAG,CAACZ;YACvB;YACAM,kBAAkBM,GAAG,CAACH;QACxB;QAEA,IAAID,iBAAiBK,IAAI,GAAG,GAAG;YAC7B,MAAM,qBAKL,CALK,IAAIrG,MACR,CAAC,kEAAkE,CAAC,GAClE,GAAG;mBAAIgG;aAAiB,CAAC/K,IAAI,CAAC,MAAM,EAAE,CAAC,GACvC,CAAC,yCAAyC,CAAC,GAC3C,CAAC,wEAAwE,CAAC,GAJxE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;QAEA,2CAA2C;QAC3CqG,KAAKsD,OAAO,GAAG;YACbtD,KAAKwD,aAAa;eACfxD,KAAKsD,OAAO,CAACI,MAAM,CAAC,CAACQ,SAAWA,WAAWlE,KAAKwD,aAAa;SACjE;QAED,MAAMwB,sBAAsB,OAAOhF,KAAKiF,eAAe;QAEvD,IACED,wBAAwB,aACxBA,wBAAwB,aACxB;YACA,MAAM,qBAEL,CAFK,IAAItG,MACR,CAAC,yEAAyE,EAAEsG,oBAAoB,2EAA2E,CAAC,GADxK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAI9G,OAAOkE,aAAa,KAAK,WAASlE,wBAAAA,OAAOkE,aAAa,qBAApBlE,sBAAsBoE,QAAQ,GAAE;QACpE,MAAM,EAAEA,QAAQ,EAAE,GAAGpE,OAAOkE,aAAa;QACzC,MAAM8C,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,IAAI,CAACA,cAAczD,QAAQ,CAACa,WAAW;YACrC,MAAM,qBAIL,CAJK,IAAI5D,MACR,CAAC,0DAA0D,EAAEwG,cAAcvL,IAAI,CAC7E,MACA,WAAW,EAAE2I,UAAU,GAHrB,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,IAAIpE,OAAOb,YAAY,EAAE;YAElB7C,8BAGkBA,uCAAAA,8BAKpBA,wCAAAA;QATH0D,OAAOb,YAAY,CAAC8H,SAAS,GAAG;gBAC3B3K,+BAAAA,cAAc6C,YAAY,qBAA1B7C,6BAA4B2K,SAAS,AAAxC;YACA,GAAGjH,OAAOb,YAAY,CAAC8H,SAAS;QAClC;QACA,MAAMC,kBAAiB5K,+BAAAA,cAAc6C,YAAY,sBAA1B7C,wCAAAA,6BAA4B2K,SAAS,qBAArC3K,qCAAuC,CAAC,UAAU;QACzE,IACE,CAAC4K,kBACDA,eAAeC,UAAU,KAAKtI,aAC9BqI,eAAeE,MAAM,KAAKvI,aAC1B,GAACvC,+BAAAA,cAAc6C,YAAY,sBAA1B7C,yCAAAA,6BAA4B+K,UAAU,qBAAtC/K,uCAAwCgL,MAAM,GAC/C;YACA,MAAM,qBAA0C,CAA1C,IAAI9G,MAAM,kCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QACjD;QACA,MAAM+G,0BAA0BvH,OAAOb,YAAY,CAAC8H,SAAS,CAAC,UAAU;QACxE,IAAI,CAACM,yBAAyB;YAC5BvH,OAAOb,YAAY,CAAC8H,SAAS,CAAC,UAAU,GAAGC;QAC7C,OAAO;YACL,IAAIK,wBAAwBC,KAAK,KAAK3I,WAAW;oBACvBmB,iCAEH1D,yCAAAA;gBAFrB,MAAMmL,mBAAkBzH,kCAAAA,OAAOb,YAAY,CAACkI,UAAU,qBAA9BrH,gCAAgCsH,MAAM;gBAC9DC,wBAAwBC,KAAK,GAC3BC,qBAAmBnL,+BAAAA,cAAc6C,YAAY,sBAA1B7C,0CAAAA,6BAA4B+K,UAAU,qBAAtC/K,wCAAwCgL,MAAM;YACrE;YACA,IAAIC,wBAAwBJ,UAAU,KAAKtI,WAAW;gBACpD0I,wBAAwBJ,UAAU,GAAGD,eAAeC,UAAU;YAChE;YACA,IAAII,wBAAwBH,MAAM,KAAKvI,WAAW;gBAChD0I,wBAAwBH,MAAM,GAC5BpH,OAAO0H,UAAU,IAAIR,eAAeE,MAAM;YAC9C;QACF;QACA,+CAA+C;QAC/C,MAAMO,0BAA0B3H,OAAOb,YAAY,CAAC8H,SAAS,CAAC,UAAU;QACxE,IACEU,2BACAA,wBAAwBH,KAAK,KAAK3I,WAClC;gBAGyBmB,kCAEH1D,yCAAAA;YAJtB,iFAAiF;YACjF,wDAAwD;YACxD,MAAMsL,oBAAmB5H,mCAAAA,OAAOb,YAAY,CAACkI,UAAU,qBAA9BrH,iCAAgC6H,OAAO;YAChEF,wBAAwBH,KAAK,GAC3BI,sBAAoBtL,+BAAAA,cAAc6C,YAAY,sBAA1B7C,0CAAAA,6BAA4B+K,UAAU,qBAAtC/K,wCAAwCuL,OAAO;QACvE;IACF;IAEA,KAAI7H,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqB8H,aAAa,EAAE;QACtC,MAAMC,0BAA0B;QAEhC,IAAI,OAAO/H,OAAOb,YAAY,CAAC2I,aAAa,KAAK,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAItH,MACR,CAAC,+GAA+G,EAAE0F,KAAKC,SAAS,CAACnG,OAAOb,YAAY,CAAC2I,aAAa,GAAG,GADjK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAME,cAAc7H,OAAOC,IAAI,CAACJ,OAAOb,YAAY,CAAC2I,aAAa;QACjE,MAAMG,sBAA8D,EAAE;QAEtE,KAAK,MAAMrJ,OAAOoJ,YAAa;YAC7B,IAAI,CAACD,wBAAwBG,IAAI,CAACtJ,MAAM;gBACtCqJ,oBAAoBlF,IAAI,CAAC;oBACvBnE;oBACAP,QAAQ;gBACV;YACF,OAAO;gBACL,MAAM8J,cAAc,AAClBnI,OAAOb,YAAY,CAAC2I,aAAa,AAGlC,CAAClJ,IAAI;gBAEN,IAAIuJ,eAAe,CAAC7M,WAAW6M,cAAc;oBAC3CF,oBAAoBlF,IAAI,CAAC;wBACvBnE;wBACAP,QAAQ,CAAC,qDAAqD,EAAE8J,aAAa;oBAC/E;gBACF;YACF;YACA,IAAIF,oBAAoB3I,MAAM,EAAE;gBAC9B,MAAM,qBAEL,CAFK,IAAIkB,MACR,CAAC,oEAAoE,EAAEyH,oBAAoBhF,GAAG,CAAC,CAACwC,OAAS,GAAG7G,IAAI,EAAE,EAAE6G,KAAKpH,MAAM,EAAE,EAAE5C,IAAI,CAAC,OAAO,GAD3I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAM2M,gCAAgCpI,OAAOqI,iBAAiB;IAC9D,kJAAkJ;IAClJ,6EAA6E;IAC7ErI,OAAOqI,iBAAiB,GAAG;QACzB,GAAID,iCAAiC,CAAC,CAAC;QACvC,gFAAgF;QAChF,uBAAuB;YACrBE,WAAW;QACb;QACAC,QAAQ;YACND,WAAW;QACb;IACF;IAEA,MAAME,qCACJxI,EAAAA,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqByI,sBAAsB,KAAI,EAAE;IAEnDzI,OAAOb,YAAY,CAACsJ,sBAAsB,GAAG;WACxC,IAAIlC,IAAI;eACNiC;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,4EAA4E;YAC5E,mCAAmC;YACnC,0EAA0E;YAC1E,wBAAwB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;KACF;IAED,IAAI,CAACxI,OAAO0I,eAAe,EAAE;QAC3B,oGAAoG;QACpG1I,OAAO0I,eAAe,GAAGrL;IAC3B;IAEA,4EAA4E;IAC5E,8EAA8E;IAC9E,2BAA2B;IAC3B,IAAI2C,OAAOb,YAAY,CAACwJ,QAAQ,KAAK9J,WAAW;QAC9CmB,OAAOb,YAAY,CAACwJ,QAAQ,GAAG3I,OAAOb,YAAY,CAACiC,SAAS;IAC9D;IAEA,+CAA+C;IAC/C,IAAIpB,OAAOb,YAAY,CAACiC,SAAS,EAAE;YAE/BrB,0BACAA;QAFF,IACEA,EAAAA,2BAAAA,WAAWZ,YAAY,qBAAvBY,yBAAyBoB,GAAG,MAAK,SACjCpB,EAAAA,4BAAAA,WAAWZ,YAAY,qBAAvBY,0BAAyBoB,GAAG,MAAK,eACjC;gBAEsDpB;YADtD,MAAM,qBAEL,CAFK,IAAIS,MACR,CAAC,kCAAkC,EAAE0F,KAAKC,SAAS,EAACpG,4BAAAA,WAAWZ,YAAY,qBAAvBY,0BAAyBoB,GAAG,EAAE,qGAAqG,CAAC,GADpL,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAnB,OAAOb,YAAY,CAACgC,GAAG,GAAG;IAC5B;IAEA,OAAOnB;AACT;AAEA,eAAe4I,kBACbzK,MAA0B,EAC1B0K,KAAa,EACbvK,MAAe;QAMbH;IAJF,IACE,kDAAkD;IAClD,gDAAgD;IAChD;QAAC/B;QAAwBC;KAAwB,CAACkH,QAAQ,CAACsF,YAC3D1K,uBAAAA,OAAOgB,YAAY,qBAAnBhB,qBAAqB2K,WAAW,GAChC;QACA,MAAMC,aAAatL,eACjB,MAAM,MAAM,CACV5B,cAAcmN,QAAQpN,OAAO,CAACuC,OAAOgB,YAAY,CAAC2J,WAAW,GAAGG,IAAI;QAIxE,IAAI,OAAOF,WAAWG,YAAY,KAAK,YAAY;YACjD,IAAI,CAAC5K,QAAQ;gBACXvC,IAAIoN,IAAI,CAAC,CAAC,2BAA2B,EAAEJ,WAAWK,IAAI,EAAE;YAC1D;YACAjL,SAAS,MAAM4K,WAAWG,YAAY,CAAC/K;QACzC;IACF;IACA,OAAOA;AACT;AAEA,eAAe,eAAekL,WAC5BR,KAAa,EACb/I,GAAW,EACX,EACEwJ,YAAY,EACZC,SAAS,EACTjL,SAAS,IAAI,EACbkL,0BAA0B,EAC1BC,wBAAwB,EACxBC,cAAc,EAUf,GAAG,CAAC,CAAC;IAEN,IAAI,CAACnI,QAAQC,GAAG,CAACmI,4BAA4B,EAAE;QAC7C,IAAI;YACFnN;QACF,EAAE,OAAOoN,KAAK;YACZ,gDAAgD;YAChD,yBAAyB;YACzB,IAAI,CAACrI,QAAQC,GAAG,CAACqI,gCAAgC,EAAE;gBACjD,MAAMD;YACR;QACF;IACF;IAEA,IAAIrI,QAAQC,GAAG,CAACqI,gCAAgC,EAAE;QAChD,2DAA2D;QAC3D,2BAA2B;QAC3B,OAAO3D,KAAK4D,KAAK,CAACvI,QAAQC,GAAG,CAACqI,gCAAgC;IAChE;IAEA,MAAME,SAASzL,SACX;QACEc,MAAM,KAAO;QACb+J,MAAM,KAAO;QACbxL,OAAO,KAAO;IAChB,IACA5B;IAEJW,cAAcoD,KAAK+I,UAAU3M,0BAA0B6N;IAEvD,IAAI7K,iBAAiB;IAErB,IAAIoK,cAAc;QAChB,OAAO,MAAMV,kBACX/I,eACEC,KACA;YACEkK,cAAc;YACd9K;YACA,GAAGoK,YAAY;QACjB,GACAhL,SAEFuK,OACAvK;IAEJ;IAEA,MAAML,OAAO,MAAMnC,OAAOG,cAAc;QAAEgO,KAAKnK;IAAI;IAEnD,2BAA2B;IAC3B,IAAI7B,wBAAAA,KAAMqB,MAAM,EAAE;YAsHZS,iBAcFA,gCAAAA,0BACCA,iCAAAA,2BAmBCA,2BAoBAA;QA3KJb,iBAAiB3D,SAAS0C;QAE1B,IAAIiM;QACJ,IAAI;YACF,MAAMC,YAAYhK,OAAOiK,MAAM,CAAC,CAAC,GAAG7I,QAAQC,GAAG;YAE/C,uEAAuE;YACvE,sEAAsE;YACtE,8BAA8B;YAC9B,IAAID,QAAQC,GAAG,CAAC6I,gBAAgB,KAAK,QAAQ;gBAC3C,4DAA4D;gBAC5D,0DAA0D;gBAC1D,8CAA8C;gBAC9CH,mBAAmBlB,QAAQ/K;YAC7B,OAAO,IAAIiB,mBAAmB,kBAAkB;gBAC9CgL,mBAAmB,MAAMhN,gBAAgB;oBACvCoN,gBAAgBrM;oBAChBiB;oBACA+K,KAAKnK;gBACP;YACF,OAAO;gBACLoK,mBAAmB,MAAM,MAAM,CAACrO,cAAcoC,MAAMgL,IAAI;YAC1D;YACA,MAAMsB,SAA6B,CAAC;YAEpC,KAAK,MAAM3L,OAAOuB,OAAOC,IAAI,CAACmB,QAAQC,GAAG,EAAG;gBAC1C,IAAI2I,SAAS,CAACvL,IAAI,KAAK2C,QAAQC,GAAG,CAAC5C,IAAI,EAAE;oBACvC2L,MAAM,CAAC3L,IAAI,GAAG2C,QAAQC,GAAG,CAAC5C,IAAI;gBAChC;YACF;YACAjC,iBAAiB4N;YAEjB,IAAIhB,WAAW;gBACb,OAAOW;YACT;QACF,EAAE,OAAON,KAAK;YACZ,0EAA0E;YAC1EG,OAAOpM,KAAK,CACV,CAAC,eAAe,EAAEuB,eAAe,uEAAuE,CAAC;YAE3G,MAAM0K;QACR;QAEA,MAAMY,eAAerK,OAAOsK,MAAM,CAC/B,MAAMlO,gBACLsM,OACApL,eAAeyM;QAInB,MAAMQ,iCAAkE,EAAE;QAE1E,IAAIlB,8BAA8BgB,aAAarL,YAAY,EAAE;YAC3D,KAAK,MAAMiK,QAAQjJ,OAAOC,IAAI,CAC5BoK,aAAarL,YAAY,EACQ;gBACjC,MAAMoB,QAAQiK,aAAarL,YAAY,CAACiK,KAAK;gBAE7C,IAAIA,SAAS,WAAW,CAAC7H,QAAQC,GAAG,CAACmJ,SAAS,EAAE;oBAE9C;gBACF;gBAEAC,iCACEF,gCACAtB,MACA7I;YAEJ;QACF;QAEA,kEAAkE;QAClE,MAAMR,aAAa8K,YAAYL;QAE/B,IAAI,CAACjJ,QAAQC,GAAG,CAACsJ,YAAY,EAAE;YAC7B,iEAAiE;YACjE,MAAM,EAAEC,YAAY,EAAE,GACpB/B,QAAQ;YACV,MAAMgC,QAAQD,aAAaE,SAAS,CAAClL;YAErC,IAAIiL,MAAME,OAAO,KAAK,OAAO;gBAC3B,uBAAuB;gBACvB,MAAMC,WAAW;oBAAC,CAAC,QAAQ,EAAEjM,eAAe,mBAAmB,CAAC;iBAAC;gBAEjE,MAAM,CAACkM,eAAexN,WAAW,GAAGF,6BAClCsN,MAAMrN,KAAK;gBAEb,kBAAkB;gBAClB,KAAK,MAAMA,SAASyN,cAAe;oBACjCD,SAASpI,IAAI,CAAC,CAAC,IAAI,EAAEpF,OAAO;gBAC9B;gBAEA,uBAAuB;gBACvBwN,SAASpI,IAAI,CACX;gBAGF,IAAInF,YAAY;oBACd,KAAK,MAAMI,WAAWmN,SAAU;wBAC9BxF,QAAQhI,KAAK,CAACK;oBAChB;oBACA,MAAMpB,aAAa;gBACrB,OAAO;oBACL,KAAK,MAAMoB,WAAWmN,SAAU;wBAC9BpB,OAAO3K,IAAI,CAACpB;oBACd;gBACF;YACF;QACF;QAEA,IAAI+B,WAAWsL,MAAM,IAAItL,WAAWsL,MAAM,KAAK,UAAU;YACvD,MAAM,qBAGL,CAHK,IAAI7K,MACR,CAAC,gDAAgD,EAAEtB,eAAe,GAAG,CAAC,GACpE,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,KAAIa,kBAAAA,WAAWuC,GAAG,qBAAdvC,gBAAgBwC,aAAa,EAAE;YACjC,MAAM,EAAEA,aAAa,EAAE,GAAGxC,WAAWuC,GAAG,IAAK,CAAC;YAC9CvC,WAAWuC,GAAG,GAAGvC,WAAWuC,GAAG,IAAI,CAAC;YACpCvC,WAAWuC,GAAG,CAACC,aAAa,GAC1B,AAACA,CAAAA,CAAAA,iCAAAA,cAAeF,QAAQ,CAAC,QACrBE,cAAc+I,KAAK,CAAC,GAAG,CAAC,KACxB/I,aAAY,KAAM;QAC1B;QAEA,IAAIkH,0BAA0B;YAC5B1J,WAAW0J,wBAAwB,GAAGA;QACxC;QAEA,IACE1J,EAAAA,2BAAAA,WAAWZ,YAAY,sBAAvBY,iCAAAA,yBAAyBwL,KAAK,qBAA9BxL,+BAAgCyL,OAAO,KACvC,GAACzL,4BAAAA,WAAWZ,YAAY,sBAAvBY,kCAAAA,0BAAyBwL,KAAK,qBAA9BxL,gCAAgC0L,KAAK,GACtC;YACA1B,OAAO3K,IAAI,CACT,sIACE,uFACA,4FACA;YAGJ,MAAMqM,QAA+C,CAAC;YACtD,KAAK,MAAM,CAAC3K,KAAK0K,QAAQ,IAAIrL,OAAOuL,OAAO,CACzC3L,WAAWZ,YAAY,CAACoM,KAAK,CAACC,OAAO,EACpC;gBACDC,KAAK,CAAC,MAAM3K,IAAI,GAAG0K;YACrB;YAEAzL,WAAWZ,YAAY,CAACoM,KAAK,CAACE,KAAK,GAAGA;QACxC;QAEA,KAAI1L,4BAAAA,WAAWZ,YAAY,qBAAvBY,0BAAyBwL,KAAK,EAAE;YAClCxB,OAAO3K,IAAI,CACT;YAGF,kEAAkE;YAClEW,WAAW6E,SAAS,GAAG;gBACrB,GAAG7E,WAAWZ,YAAY,CAACoM,KAAK;gBAChC,GAAGxL,WAAW6E,SAAS;YACzB;YACA7E,WAAWZ,YAAY,CAACwM,oBAAoB,KAC1C5L,WAAWZ,YAAY,CAACoM,KAAK,CAACK,WAAW;YAC3C7L,WAAWZ,YAAY,CAAC0M,eAAe,KACrC9L,WAAWZ,YAAY,CAACoM,KAAK,CAACO,MAAM;YACtC/L,WAAWZ,YAAY,CAAC4M,oBAAoB,KAC1ChM,WAAWZ,YAAY,CAACoM,KAAK,CAACS,WAAW;YAC3CjM,WAAWZ,YAAY,CAAC8M,mBAAmB,KACzClM,WAAWZ,YAAY,CAACoM,KAAK,CAACW,UAAU;QAC5C;QAEA,KAAInM,4BAAAA,WAAWZ,YAAY,qBAAvBY,0BAAyBoM,eAAe,EAAE;gBAGf,MAAC;YAF9B,MAAM,EAAEC,YAAY,EAAE,GACpBpD,QAAQ;YACV,MAAMqD,wBAAwB,QAAA,MAAMD,oCAAP,OAAA,AAAC,MAAuBE,GAAG,qBAA3B,KAA6BC,SAAS;YAEnE,IAAI,CAACF,sBAAsB;gBACzBtC,OAAO3K,IAAI,CACT,CAAC,+GAA+G,CAAC;gBAEnHW,WAAWZ,YAAY,CAACgN,eAAe,GAAG;YAC5C;QACF;QAEA,yCAAyC;QACzC,IAAIpM,CAAAA,8BAAAA,WAAY2I,eAAe,aAAY8D,QAAQ;YACjD,oGAAoG;YACpGzM,WAAW2I,eAAe,GAAG3I,WAAW2I,eAAe,CAAC+D,MAAM;QAChE;QAEA,IACE1M,WAAWZ,YAAY,IACvBY,WAAWZ,YAAY,CAACuN,yBAAyB,KAAK7N,aACtDkB,WAAWZ,YAAY,CAACiC,SAAS,KAAK,MACtC;YACArB,WAAWZ,YAAY,CAACuN,yBAAyB,GAAG;YACpD9B,iCACEF,gCACA,6BACA,MACA;QAEJ;QAEA,IACEhB,kBACCb,CAAAA,UAAUzM,0BAA0ByM,UAAU1M,YAAW,GAC1D;YACA4D,WAAWZ,YAAY,KAAK,CAAC;YAE7BwN,wCACE5M,WAAWZ,YAAY,EACvB,oBACA,MACAqK,6BAA6BkB,iCAAiC7L;YAGhE8N,wCACE5M,WAAWZ,YAAY,EACvBoC,QAAQC,GAAG,CAACmJ,SAAS,GAAG,oBAAoB,sBAC5C,OACAnB,6BAA6BkB,iCAAiC7L;YAGhE8N,wCACE5M,WAAWZ,YAAY,EACvB,6BACA,MACAqK,6BAA6BkB,iCAAiC7L;YAGhE8N,wCACE5M,WAAWZ,YAAY,EACvB,sBACA,OACAqK,6BAA6BkB,iCAAiC7L;QAElE;QAEA,IAAI2K,4BAA4B;YAC9BA,2BAA2BkB;QAC7B;QAEA,MAAMkC,iBAAiB/M,eACrBC,KACA;YACEkK,cAActO,SAASoE,KAAK7B;YAC5B4O,YAAY5O;YACZiB;YACA,GAAGa,UAAU;QACf,GACAzB;QAEF,OAAO,MAAMsK,kBAAkBgE,gBAAgB/D,OAAOvK;IACxD,OAAO;QACL,MAAMwO,iBAAiBvR,SAASU,YAAY,CAAC,EAAE,EAAET,QAAQS,YAAY,CAAC,EAAE;QACxE,MAAM8Q,oBAAoBjR,OAAOkR,IAAI,CACnC;YACE,GAAGF,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,KAAK,CAAC;YACxB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;SACxB,EACD;YAAE7C,KAAKnK;QAAI;QAEb,IAAIiN,qCAAAA,kBAAmBzN,MAAM,EAAE;YAC7B,MAAM,qBAIL,CAJK,IAAIkB,MACR,CAAC,yBAAyB,EAAEjF,SAC1BwR,mBACA,0GAA0G,CAAC,GAHzG,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,qDAAqD;IACrD,iEAAiE;IACjE,MAAMH,iBAAiB/M,eACrBC,KACA;QAAE,GAAGxD,aAAa;QAAE4C;IAAe,GACnCZ;IAEFxB,6BAA6B8P;IAC7B,OAAO,MAAMhE,kBAAkBgE,gBAAgB/D,OAAOvK;AACxD;AAQA,OAAO,SAASsM,iCAGdF,8BAA+D,EAC/D9L,GAAY,EACZ2B,KAAkC,EAClClC,MAAe;IAEf,IAAIkC,UAAU,AAACjE,cAAc6C,YAAY,AAA4B,CAACP,IAAI,EAAE;QAC1E8L,+BAA+B3H,IAAI,CAAC;YAAEnE;YAAK2B;YAAOlC;QAAO;IAC3D;AACF;AAEA,SAASsO,wCAGPM,kBAAsC,EACtCrO,GAAY,EACZ2B,KAAkC,EAClCmK,8BAA2E;IAE3E,IAAIuC,kBAAkB,CAACrO,IAAI,KAAK2B,OAAO;QACrC0M,kBAAkB,CAACrO,IAAI,GAAG2B;QAE1B,IAAImK,gCAAgC;YAClC,MAAMwC,SACJ3M,UAAU,OAAO,YAAYA,UAAU,QAAQ,aAAa;YAE9D,MAAMlC,SAAS,GAAG6O,OAAO,yBAAyB,CAAC;YAEnDtC,iCACEF,gCACA9L,KACA2B,OACAlC;QAEJ;IACF;AACF;AAEA,SAASwM,YAAYsC,GAAQ;IAC3B,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;QAC3C,OAAOA;IACT;IAEA,IAAIxM,MAAMC,OAAO,CAACuM,MAAM;QACtB,OAAOA,IAAIlK,GAAG,CAAC4H;IACjB;IACA,MAAMzK,OAAOD,OAAOC,IAAI,CAAC+M;IACzB,IAAI/M,KAAKd,MAAM,KAAK,GAAG;QACrB,OAAO6N;IACT;IAEA,OAAO/M,KAAKC,MAAM,CAAC,CAAC+M,KAAKxO;;QACrBwO,GAAW,CAACxO,IAAI,GAAGiM,YAAYsC,GAAG,CAACvO,IAAI;QACzC,OAAOwO;IACT,GAAG,CAAC;AACN", "ignoreList": [0]}