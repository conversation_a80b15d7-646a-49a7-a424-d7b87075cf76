{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,YAAY,CAAEC,eAAe", "ignoreList": [0]}