{"version": 3, "sources": ["../../../src/server/web/globals.ts"], "sourcesContent": ["import type {\n  InstrumentationModule,\n  InstrumentationOnRequestError,\n} from '../instrumentation/types'\n\ndeclare const _ENTRIES: any\n\nexport async function getEdgeInstrumentationModule(): Promise<\n  InstrumentationModule | undefined\n> {\n  const instrumentation =\n    '_ENTRIES' in globalThis &&\n    _ENTRIES.middleware_instrumentation &&\n    (await _ENTRIES.middleware_instrumentation)\n\n  return instrumentation\n}\n\nlet instrumentationModulePromise: Promise<any> | null = null\nasync function registerInstrumentation() {\n  // Ensure registerInstrumentation is not called in production build\n  if (process.env.NEXT_PHASE === 'phase-production-build') return\n  if (!instrumentationModulePromise) {\n    instrumentationModulePromise = getEdgeInstrumentationModule()\n  }\n  const instrumentation = await instrumentationModulePromise\n  if (instrumentation?.register) {\n    try {\n      await instrumentation.register()\n    } catch (err: any) {\n      err.message = `An error occurred while loading instrumentation hook: ${err.message}`\n      throw err\n    }\n  }\n}\n\nexport async function edgeInstrumentationOnRequestError(\n  ...args: Parameters<InstrumentationOnRequestError>\n) {\n  const instrumentation = await getEdgeInstrumentationModule()\n  try {\n    await instrumentation?.onRequestError?.(...args)\n  } catch (err) {\n    // Log the soft error and continue, since the original error has already been thrown\n    console.error('Error in instrumentation.onRequestError:', err)\n  }\n}\n\nlet registerInstrumentationPromise: Promise<void> | null = null\nexport function ensureInstrumentationRegistered() {\n  if (!registerInstrumentationPromise) {\n    registerInstrumentationPromise = registerInstrumentation()\n  }\n  return registerInstrumentationPromise\n}\n\nfunction getUnsupportedModuleErrorMessage(module: string) {\n  // warning: if you change these messages, you must adjust how dev-overlay's middleware detects modules not found\n  return `The edge runtime does not support Node.js '${module}' module.\nLearn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`\n}\n\nfunction __import_unsupported(moduleName: string) {\n  const proxy: any = new Proxy(function () {}, {\n    get(_obj, prop) {\n      if (prop === 'then') {\n        return {}\n      }\n      throw new Error(getUnsupportedModuleErrorMessage(moduleName))\n    },\n    construct() {\n      throw new Error(getUnsupportedModuleErrorMessage(moduleName))\n    },\n    apply(_target, _this, args) {\n      if (typeof args[0] === 'function') {\n        return args[0](proxy)\n      }\n      throw new Error(getUnsupportedModuleErrorMessage(moduleName))\n    },\n  })\n  return new Proxy({}, { get: () => proxy })\n}\n\nfunction enhanceGlobals() {\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    return\n  }\n\n  // The condition is true when the \"process\" module is provided\n  if (process !== global.process) {\n    // prefer local process but global.process has correct \"env\"\n    process.env = global.process.env\n    global.process = process\n  }\n\n  // to allow building code that import but does not use node.js modules,\n  // webpack will expect this function to exist in global scope\n  Object.defineProperty(globalThis, '__import_unsupported', {\n    value: __import_unsupported,\n    enumerable: false,\n    configurable: false,\n  })\n\n  // Eagerly fire instrumentation hook to make the startup faster.\n  void ensureInstrumentationRegistered()\n}\n\nenhanceGlobals()\n"], "names": ["getEdgeInstrumentationModule", "instrumentation", "globalThis", "_ENTRIES", "middleware_instrumentation", "instrumentationModulePromise", "registerInstrumentation", "process", "env", "NEXT_PHASE", "register", "err", "message", "edgeInstrumentationOnRequestError", "args", "onRequestError", "console", "error", "registerInstrumentationPromise", "ensureInstrumentationRegistered", "getUnsupportedModuleErrorMessage", "module", "__import_unsupported", "moduleName", "proxy", "Proxy", "get", "_obj", "prop", "Error", "construct", "apply", "_target", "_this", "enhanceGlobals", "NEXT_RUNTIME", "global", "Object", "defineProperty", "value", "enumerable", "configurable"], "mappings": "AAOA,OAAO,eAAeA;IAGpB,MAAMC,kBACJ,cAAcC,cACdC,SAASC,0BAA0B,IAClC,MAAMD,SAASC,0BAA0B;IAE5C,OAAOH;AACT;AAEA,IAAII,+BAAoD;AACxD,eAAeC;IACb,mEAAmE;IACnE,IAAIC,QAAQC,GAAG,CAACC,UAAU,KAAK,0BAA0B;IACzD,IAAI,CAACJ,8BAA8B;QACjCA,+BAA+BL;IACjC;IACA,MAAMC,kBAAkB,MAAMI;IAC9B,IAAIJ,mCAAAA,gBAAiBS,QAAQ,EAAE;QAC7B,IAAI;YACF,MAAMT,gBAAgBS,QAAQ;QAChC,EAAE,OAAOC,KAAU;YACjBA,IAAIC,OAAO,GAAG,CAAC,sDAAsD,EAAED,IAAIC,OAAO,EAAE;YACpF,MAAMD;QACR;IACF;AACF;AAEA,OAAO,eAAeE,kCACpB,GAAGC,IAA+C;IAElD,MAAMb,kBAAkB,MAAMD;IAC9B,IAAI;YACIC;QAAN,OAAMA,oCAAAA,kCAAAA,gBAAiBc,cAAc,qBAA/Bd,qCAAAA,oBAAqCa;IAC7C,EAAE,OAAOH,KAAK;QACZ,oFAAoF;QACpFK,QAAQC,KAAK,CAAC,4CAA4CN;IAC5D;AACF;AAEA,IAAIO,iCAAuD;AAC3D,OAAO,SAASC;IACd,IAAI,CAACD,gCAAgC;QACnCA,iCAAiCZ;IACnC;IACA,OAAOY;AACT;AAEA,SAASE,iCAAiCC,MAAc;IACtD,gHAAgH;IAChH,OAAO,CAAC,2CAA2C,EAAEA,OAAO;wEACU,CAAC;AACzE;AAEA,SAASC,qBAAqBC,UAAkB;IAC9C,MAAMC,QAAa,IAAIC,MAAM,YAAa,GAAG;QAC3CC,KAAIC,IAAI,EAAEC,IAAI;YACZ,IAAIA,SAAS,QAAQ;gBACnB,OAAO,CAAC;YACV;YACA,MAAM,qBAAuD,CAAvD,IAAIC,MAAMT,iCAAiCG,cAA3C,qBAAA;uBAAA;4BAAA;8BAAA;YAAsD;QAC9D;QACAO;YACE,MAAM,qBAAuD,CAAvD,IAAID,MAAMT,iCAAiCG,cAA3C,qBAAA;uBAAA;4BAAA;8BAAA;YAAsD;QAC9D;QACAQ,OAAMC,OAAO,EAAEC,KAAK,EAAEnB,IAAI;YACxB,IAAI,OAAOA,IAAI,CAAC,EAAE,KAAK,YAAY;gBACjC,OAAOA,IAAI,CAAC,EAAE,CAACU;YACjB;YACA,MAAM,qBAAuD,CAAvD,IAAIK,MAAMT,iCAAiCG,cAA3C,qBAAA;uBAAA;4BAAA;8BAAA;YAAsD;QAC9D;IACF;IACA,OAAO,IAAIE,MAAM,CAAC,GAAG;QAAEC,KAAK,IAAMF;IAAM;AAC1C;AAEA,SAASU;IACP,IAAI3B,QAAQC,GAAG,CAAC2B,YAAY,KAAK,QAAQ;QACvC;IACF;IAEA,8DAA8D;IAC9D,IAAI5B,YAAY6B,OAAO7B,OAAO,EAAE;QAC9B,4DAA4D;QAC5DA,QAAQC,GAAG,GAAG4B,OAAO7B,OAAO,CAACC,GAAG;QAChC4B,OAAO7B,OAAO,GAAGA;IACnB;IAEA,uEAAuE;IACvE,6DAA6D;IAC7D8B,OAAOC,cAAc,CAACpC,YAAY,wBAAwB;QACxDqC,OAAOjB;QACPkB,YAAY;QACZC,cAAc;IAChB;IAEA,gEAAgE;IAChE,KAAKtB;AACP;AAEAe", "ignoreList": [0]}