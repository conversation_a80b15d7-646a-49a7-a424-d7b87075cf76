{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactServerDOMWebpackServer\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMWebpackServer"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,YAAY,CAAEC,2BAA2B", "ignoreList": [0]}