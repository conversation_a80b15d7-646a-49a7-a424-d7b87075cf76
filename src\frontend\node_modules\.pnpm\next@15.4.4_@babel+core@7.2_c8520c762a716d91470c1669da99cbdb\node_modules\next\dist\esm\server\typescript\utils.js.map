{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "sourcesContent": ["import path from 'path'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\ntype TypeScript = typeof import('typescript/lib/tsserverlibrary')\n\nlet ts: TypeScript\nlet info: tsModule.server.PluginCreateInfo\nlet appDirRegExp: RegExp\n\nexport function log(message: string) {\n  info.project.projectService.logger.info('[next] ' + message)\n}\n\n// This function has to be called initially.\nexport function init(opts: {\n  ts: TypeScript\n  info: tsModule.server.PluginCreateInfo\n}) {\n  const projectDir = opts.info.project.getCurrentDirectory()\n  ts = opts.ts\n  info = opts.info\n  appDirRegExp = new RegExp(\n    '^' + (projectDir + '(/src)?/app').replace(/[\\\\/]/g, '[\\\\/]')\n  )\n\n  log('Initialized Next.js TypeScript plugin: ' + projectDir)\n}\n\nexport function getTs() {\n  return ts\n}\n\nexport function getInfo() {\n  return info\n}\n\nexport function getTypeChecker() {\n  const program = info.languageService.getProgram()\n  if (!program) {\n    log('Failed to get program while while running getTypeChecker.')\n    return\n  }\n  const typeChecker = program.getTypeChecker()\n  if (!typeChecker) {\n    log('Failed to get type checker while running getTypeChecker.')\n    return\n  }\n  return typeChecker\n}\n\nexport function getSource(fileName: string) {\n  const program = info.languageService.getProgram()\n  if (!program) {\n    log('Failed to get program while running getSource for: ' + fileName)\n    return\n  }\n\n  const sourceFile = program.getSourceFile(fileName)\n  if (!sourceFile) {\n    log('Failed to get source file while running getSource for: ' + fileName)\n    return\n  }\n\n  return sourceFile\n}\n\nexport function removeStringQuotes(str: string): string {\n  return str.replace(/^['\"`]|['\"`]$/g, '')\n}\n\nexport const isPositionInsideNode = (position: number, node: tsModule.Node) => {\n  const start = node.getFullStart()\n  return start <= position && position <= node.getFullWidth() + start\n}\n\nexport const isDefaultFunctionExport = (\n  node: tsModule.Node\n): node is tsModule.FunctionDeclaration => {\n  if (ts.isFunctionDeclaration(node)) {\n    let hasExportKeyword = false\n    let hasDefaultKeyword = false\n\n    if (node.modifiers) {\n      for (const modifier of node.modifiers) {\n        if (modifier.kind === ts.SyntaxKind.ExportKeyword) {\n          hasExportKeyword = true\n        } else if (modifier.kind === ts.SyntaxKind.DefaultKeyword) {\n          hasDefaultKeyword = true\n        }\n      }\n    }\n\n    // `export default function`\n    if (hasExportKeyword && hasDefaultKeyword) {\n      return true\n    }\n  }\n  return false\n}\n\nexport const isInsideApp = (filePath: string) => {\n  return appDirRegExp.test(filePath)\n}\nexport const isAppEntryFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^(page|layout)\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\nexport const isPageFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^page\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\n\n// Check if a module is a client entry.\nexport function getEntryInfo(\n  fileName: string,\n  throwOnInvalidDirective?: boolean\n) {\n  const source = getSource(fileName)\n  if (source) {\n    let isDirective = true\n    let isClientEntry = false\n    let isServerEntry = false\n\n    ts.forEachChild(source!, (node) => {\n      if (\n        ts.isExpressionStatement(node) &&\n        ts.isStringLiteral(node.expression)\n      ) {\n        if (node.expression.text === 'use client') {\n          if (isDirective) {\n            isClientEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use client\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        } else if (node.expression.text === 'use server') {\n          if (isDirective) {\n            isServerEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use server\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        }\n\n        if (isClientEntry && isServerEntry) {\n          const e = {\n            messageText:\n              'Cannot use both \"use client\" and \"use server\" directives in the same file.',\n            start: node.expression.getStart(),\n            length: node.expression.getWidth(),\n          }\n          throw e\n        }\n      } else {\n        isDirective = false\n      }\n    })\n\n    return { client: isClientEntry, server: isServerEntry }\n  }\n\n  return { client: false, server: false }\n}\n"], "names": ["path", "ts", "info", "appDirRegExp", "log", "message", "project", "projectService", "logger", "init", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "getTs", "getInfo", "getType<PERSON><PERSON>cker", "program", "languageService", "getProgram", "typeC<PERSON>cker", "getSource", "fileName", "sourceFile", "getSourceFile", "removeStringQuotes", "str", "isPositionInsideNode", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefaultFunctionExport", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "isInsideApp", "filePath", "test", "isAppEntryFile", "basename", "isPageFile", "getEntryInfo", "throwOnInvalidDirective", "source", "isDirective", "isClientEntry", "isServerEntry", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth", "client", "server"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAKvB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEJ,OAAO,SAASC,IAAIC,OAAe;IACjCH,KAAKI,OAAO,CAACC,cAAc,CAACC,MAAM,CAACN,IAAI,CAAC,YAAYG;AACtD;AAEA,4CAA4C;AAC5C,OAAO,SAASI,KAAKC,IAGpB;IACC,MAAMC,aAAaD,KAAKR,IAAI,CAACI,OAAO,CAACM,mBAAmB;IACxDX,KAAKS,KAAKT,EAAE;IACZC,OAAOQ,KAAKR,IAAI;IAChBC,eAAe,IAAIU,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAGvDV,IAAI,4CAA4CO;AAClD;AAEA,OAAO,SAASI;IACd,OAAOd;AACT;AAEA,OAAO,SAASe;IACd,OAAOd;AACT;AAEA,OAAO,SAASe;IACd,MAAMC,UAAUhB,KAAKiB,eAAe,CAACC,UAAU;IAC/C,IAAI,CAACF,SAAS;QACZd,IAAI;QACJ;IACF;IACA,MAAMiB,cAAcH,QAAQD,cAAc;IAC1C,IAAI,CAACI,aAAa;QAChBjB,IAAI;QACJ;IACF;IACA,OAAOiB;AACT;AAEA,OAAO,SAASC,UAAUC,QAAgB;IACxC,MAAML,UAAUhB,KAAKiB,eAAe,CAACC,UAAU;IAC/C,IAAI,CAACF,SAAS;QACZd,IAAI,wDAAwDmB;QAC5D;IACF;IAEA,MAAMC,aAAaN,QAAQO,aAAa,CAACF;IACzC,IAAI,CAACC,YAAY;QACfpB,IAAI,4DAA4DmB;QAChE;IACF;IAEA,OAAOC;AACT;AAEA,OAAO,SAASE,mBAAmBC,GAAW;IAC5C,OAAOA,IAAIb,OAAO,CAAC,kBAAkB;AACvC;AAEA,OAAO,MAAMc,uBAAuB,CAACC,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE,EAAC;AAED,OAAO,MAAMG,0BAA0B,CACrCJ;IAEA,IAAI7B,GAAGkC,qBAAqB,CAACL,OAAO;QAClC,IAAIM,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIP,KAAKQ,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYT,KAAKQ,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAKvC,GAAGwC,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAKvC,GAAGwC,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT,EAAC;AAED,OAAO,MAAMO,cAAc,CAACC;IAC1B,OAAO1C,aAAa2C,IAAI,CAACD;AAC3B,EAAC;AACD,OAAO,MAAME,iBAAiB,CAACF;IAC7B,OACE1C,aAAa2C,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAAC9C,KAAKgD,QAAQ,CAACH;AAE9D,EAAC;AACD,OAAO,MAAMI,aAAa,CAACJ;IACzB,OACE1C,aAAa2C,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAAC9C,KAAKgD,QAAQ,CAACH;AAErD,EAAC;AAED,uCAAuC;AACvC,OAAO,SAASK,aACd3B,QAAgB,EAChB4B,uBAAiC;IAEjC,MAAMC,SAAS9B,UAAUC;IACzB,IAAI6B,QAAQ;QACV,IAAIC,cAAc;QAClB,IAAIC,gBAAgB;QACpB,IAAIC,gBAAgB;QAEpBtD,GAAGuD,YAAY,CAACJ,QAAS,CAACtB;YACxB,IACE7B,GAAGwD,qBAAqB,CAAC3B,SACzB7B,GAAGyD,eAAe,CAAC5B,KAAK6B,UAAU,GAClC;gBACA,IAAI7B,KAAK6B,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIP,aAAa;wBACfC,gBAAgB;oBAClB,OAAO;wBACL,IAAIH,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF,OAAO,IAAI/B,KAAK6B,UAAU,CAACC,IAAI,KAAK,cAAc;oBAChD,IAAIP,aAAa;wBACfE,gBAAgB;oBAClB,OAAO;wBACL,IAAIJ,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;gBAEA,IAAIP,iBAAiBC,eAAe;oBAClC,MAAMM,IAAI;wBACRC,aACE;wBACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;wBAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;oBAClC;oBACA,MAAMJ;gBACR;YACF,OAAO;gBACLR,cAAc;YAChB;QACF;QAEA,OAAO;YAAEa,QAAQZ;YAAea,QAAQZ;QAAc;IACxD;IAEA,OAAO;QAAEW,QAAQ;QAAOC,QAAQ;IAAM;AACxC", "ignoreList": [0]}