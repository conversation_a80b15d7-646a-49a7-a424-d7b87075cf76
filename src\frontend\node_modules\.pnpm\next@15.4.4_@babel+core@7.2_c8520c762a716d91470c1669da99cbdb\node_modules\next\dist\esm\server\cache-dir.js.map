{"version": 3, "sources": ["../../src/server/cache-dir.ts"], "sourcesContent": ["import path from 'path'\nimport isDockerFunction from 'next/dist/compiled/is-docker'\n\nexport function getStorageDirectory(distDir: string): string | undefined {\n  const isLikelyEphemeral = isDockerFunction()\n\n  if (isLikelyEphemeral) {\n    return undefined\n  }\n  return path.join(distDir, 'cache')\n}\n"], "names": ["path", "isDockerFunction", "getStorageDirectory", "distDir", "isLikelyEphemeral", "undefined", "join"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,sBAAsB,+BAA8B;AAE3D,OAAO,SAASC,oBAAoBC,OAAe;IACjD,MAAMC,oBAAoBH;IAE1B,IAAIG,mBAAmB;QACrB,OAAOC;IACT;IACA,OAAOL,KAAKM,IAAI,CAACH,SAAS;AAC5B", "ignoreList": [0]}