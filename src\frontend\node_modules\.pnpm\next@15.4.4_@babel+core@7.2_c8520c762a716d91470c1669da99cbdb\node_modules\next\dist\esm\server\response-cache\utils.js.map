{"version": 3, "sources": ["../../../src/server/response-cache/utils.ts"], "sourcesContent": ["import {\n  CachedRouteK<PERSON>,\n  IncrementalCacheKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type IncrementalResponseCacheEntry,\n  type ResponseCacheEntry,\n} from './types'\n\nimport RenderResult from '../render-result'\nimport { RouteKind } from '../route-kind'\n\nexport async function fromResponseCacheEntry(\n  cacheEntry: ResponseCacheEntry\n): Promise<IncrementalResponseCacheEntry> {\n  return {\n    ...cacheEntry,\n    value:\n      cacheEntry.value?.kind === CachedRouteKind.PAGES\n        ? {\n            kind: CachedRouteKind.PAGES,\n            html: await cacheEntry.value.html.toUnchunkedString(true),\n            pageData: cacheEntry.value.pageData,\n            headers: cacheEntry.value.headers,\n            status: cacheEntry.value.status,\n          }\n        : cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n          ? {\n              kind: CachedRouteKind.APP_PAGE,\n              html: await cacheEntry.value.html.toUnchunkedString(true),\n              postponed: cacheEntry.value.postponed,\n              rscData: cacheEntry.value.rscData,\n              headers: cacheEntry.value.headers,\n              status: cacheEntry.value.status,\n              segmentData: cacheEntry.value.segmentData,\n            }\n          : cacheEntry.value,\n  }\n}\n\nexport async function toResponseCacheEntry(\n  response: IncrementalResponseCacheEntry | null\n): Promise<ResponseCacheEntry | null> {\n  if (!response) return null\n\n  return {\n    isMiss: response.isMiss,\n    isStale: response.isStale,\n    cacheControl: response.cacheControl,\n    value:\n      response.value?.kind === CachedRouteKind.PAGES\n        ? ({\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(response.value.html),\n            pageData: response.value.pageData,\n            headers: response.value.headers,\n            status: response.value.status,\n          } satisfies CachedPageValue)\n        : response.value?.kind === CachedRouteKind.APP_PAGE\n          ? ({\n              kind: CachedRouteKind.APP_PAGE,\n              html: RenderResult.fromStatic(response.value.html),\n              rscData: response.value.rscData,\n              headers: response.value.headers,\n              status: response.value.status,\n              postponed: response.value.postponed,\n              segmentData: response.value.segmentData,\n            } satisfies CachedAppPageValue)\n          : response.value,\n  }\n}\n\nexport function routeKindToIncrementalCacheKind(\n  routeKind: RouteKind\n): Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH> {\n  switch (routeKind) {\n    case RouteKind.PAGES:\n      return IncrementalCacheKind.PAGES\n    case RouteKind.APP_PAGE:\n      return IncrementalCacheKind.APP_PAGE\n    case RouteKind.IMAGE:\n      return IncrementalCacheKind.IMAGE\n    case RouteKind.APP_ROUTE:\n      return IncrementalCacheKind.APP_ROUTE\n    default:\n      throw new Error(`Unexpected route kind ${routeKind}`)\n  }\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind", "RenderResult", "RouteKind", "fromResponseCacheEntry", "cacheEntry", "value", "kind", "PAGES", "html", "toUnchunkedString", "pageData", "headers", "status", "APP_PAGE", "postponed", "rscData", "segmentData", "toResponseCacheEntry", "response", "isMiss", "isStale", "cacheControl", "fromStatic", "routeKindToIncrementalCacheKind", "routeKind", "IMAGE", "APP_ROUTE", "Error"], "mappings": "AAAA,SACEA,eAAe,EACfC,oBAAoB,QAKf,UAAS;AAEhB,OAAOC,kBAAkB,mBAAkB;AAC3C,SAASC,SAAS,QAAQ,gBAAe;AAEzC,OAAO,eAAeC,uBACpBC,UAA8B;QAK1BA,mBAQIA;IAXR,OAAO;QACL,GAAGA,UAAU;QACbC,OACED,EAAAA,oBAAAA,WAAWC,KAAK,qBAAhBD,kBAAkBE,IAAI,MAAKP,gBAAgBQ,KAAK,GAC5C;YACED,MAAMP,gBAAgBQ,KAAK;YAC3BC,MAAM,MAAMJ,WAAWC,KAAK,CAACG,IAAI,CAACC,iBAAiB,CAAC;YACpDC,UAAUN,WAAWC,KAAK,CAACK,QAAQ;YACnCC,SAASP,WAAWC,KAAK,CAACM,OAAO;YACjCC,QAAQR,WAAWC,KAAK,CAACO,MAAM;QACjC,IACAR,EAAAA,qBAAAA,WAAWC,KAAK,qBAAhBD,mBAAkBE,IAAI,MAAKP,gBAAgBc,QAAQ,GACjD;YACEP,MAAMP,gBAAgBc,QAAQ;YAC9BL,MAAM,MAAMJ,WAAWC,KAAK,CAACG,IAAI,CAACC,iBAAiB,CAAC;YACpDK,WAAWV,WAAWC,KAAK,CAACS,SAAS;YACrCC,SAASX,WAAWC,KAAK,CAACU,OAAO;YACjCJ,SAASP,WAAWC,KAAK,CAACM,OAAO;YACjCC,QAAQR,WAAWC,KAAK,CAACO,MAAM;YAC/BI,aAAaZ,WAAWC,KAAK,CAACW,WAAW;QAC3C,IACAZ,WAAWC,KAAK;IAC1B;AACF;AAEA,OAAO,eAAeY,qBACpBC,QAA8C;QAS1CA,iBAQIA;IAfR,IAAI,CAACA,UAAU,OAAO;IAEtB,OAAO;QACLC,QAAQD,SAASC,MAAM;QACvBC,SAASF,SAASE,OAAO;QACzBC,cAAcH,SAASG,YAAY;QACnChB,OACEa,EAAAA,kBAAAA,SAASb,KAAK,qBAAda,gBAAgBZ,IAAI,MAAKP,gBAAgBQ,KAAK,GACzC;YACCD,MAAMP,gBAAgBQ,KAAK;YAC3BC,MAAMP,aAAaqB,UAAU,CAACJ,SAASb,KAAK,CAACG,IAAI;YACjDE,UAAUQ,SAASb,KAAK,CAACK,QAAQ;YACjCC,SAASO,SAASb,KAAK,CAACM,OAAO;YAC/BC,QAAQM,SAASb,KAAK,CAACO,MAAM;QAC/B,IACAM,EAAAA,mBAAAA,SAASb,KAAK,qBAAda,iBAAgBZ,IAAI,MAAKP,gBAAgBc,QAAQ,GAC9C;YACCP,MAAMP,gBAAgBc,QAAQ;YAC9BL,MAAMP,aAAaqB,UAAU,CAACJ,SAASb,KAAK,CAACG,IAAI;YACjDO,SAASG,SAASb,KAAK,CAACU,OAAO;YAC/BJ,SAASO,SAASb,KAAK,CAACM,OAAO;YAC/BC,QAAQM,SAASb,KAAK,CAACO,MAAM;YAC7BE,WAAWI,SAASb,KAAK,CAACS,SAAS;YACnCE,aAAaE,SAASb,KAAK,CAACW,WAAW;QACzC,IACAE,SAASb,KAAK;IACxB;AACF;AAEA,OAAO,SAASkB,gCACdC,SAAoB;IAEpB,OAAQA;QACN,KAAKtB,UAAUK,KAAK;YAClB,OAAOP,qBAAqBO,KAAK;QACnC,KAAKL,UAAUW,QAAQ;YACrB,OAAOb,qBAAqBa,QAAQ;QACtC,KAAKX,UAAUuB,KAAK;YAClB,OAAOzB,qBAAqByB,KAAK;QACnC,KAAKvB,UAAUwB,SAAS;YACtB,OAAO1B,qBAAqB0B,SAAS;QACvC;YACE,MAAM,qBAA+C,CAA/C,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,WAAW,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;IACxD;AACF", "ignoreList": [0]}