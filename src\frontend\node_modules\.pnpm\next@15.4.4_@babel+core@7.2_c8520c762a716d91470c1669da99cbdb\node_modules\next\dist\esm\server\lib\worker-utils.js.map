{"version": 3, "sources": ["../../../src/server/lib/worker-utils.ts"], "sourcesContent": ["import http from 'http'\n\nexport const getFreePort = async (): Promise<number> => {\n  return new Promise((resolve, reject) => {\n    const server = http.createServer(() => {})\n    server.listen(0, () => {\n      const address = server.address()\n      server.close()\n\n      if (address && typeof address === 'object') {\n        resolve(address.port)\n      } else {\n        reject(new Error('invalid address from server: ' + address?.toString()))\n      }\n    })\n  })\n}\n"], "names": ["http", "getFreePort", "Promise", "resolve", "reject", "server", "createServer", "listen", "address", "close", "port", "Error", "toString"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAEvB,OAAO,MAAMC,cAAc;IACzB,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B,MAAMC,SAASL,KAAKM,YAAY,CAAC,KAAO;QACxCD,OAAOE,MAAM,CAAC,GAAG;YACf,MAAMC,UAAUH,OAAOG,OAAO;YAC9BH,OAAOI,KAAK;YAEZ,IAAID,WAAW,OAAOA,YAAY,UAAU;gBAC1CL,QAAQK,QAAQE,IAAI;YACtB,OAAO;gBACLN,OAAO,qBAAgE,CAAhE,IAAIO,MAAM,mCAAkCH,2BAAAA,QAASI,QAAQ,MAA7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAA+D;YACxE;QACF;IACF;AACF,EAAC", "ignoreList": [0]}