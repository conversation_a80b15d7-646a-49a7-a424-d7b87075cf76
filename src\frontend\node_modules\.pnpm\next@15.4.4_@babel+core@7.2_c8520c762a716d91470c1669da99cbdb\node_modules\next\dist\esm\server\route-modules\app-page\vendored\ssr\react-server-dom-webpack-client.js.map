{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/ssr/react-server-dom-webpack-client.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMWebpackClient\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMWebpackClient"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,YAAY,CAAEC,2BAA2B", "ignoreList": [0]}