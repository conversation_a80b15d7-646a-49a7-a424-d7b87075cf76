{"version": 3, "sources": ["../../../../../../src/server/route-matcher-providers/dev/helpers/file-reader/file-reader.ts"], "sourcesContent": ["export interface FileReader {\n  /**\n   * Reads the directory contents recursively.\n   *\n   * @param dir directory to read recursively from\n   */\n  read(dir: string): Promise<ReadonlyArray<string>> | ReadonlyArray<string>\n}\n"], "names": [], "mappings": "AAAA,WAOC", "ignoreList": [0]}