{"version": 3, "sources": ["../../../../src/server/web/spec-extension/url-pattern.ts"], "sourcesContent": ["const GlobalURLPattern =\n  // @ts-expect-error: URLPattern is not available in Node.js\n  typeof URLPattern === 'undefined' ? undefined : URLPattern\n\nexport { GlobalURLPattern as URLPattern }\n"], "names": ["GlobalURLPattern", "URLPattern", "undefined"], "mappings": "AAAA,MAAMA,mBACJ,2DAA2D;AAC3D,OAAOC,eAAe,cAAcC,YAAYD;AAElD,SAASD,oBAAoBC,UAAU,GAAE", "ignoreList": [0]}