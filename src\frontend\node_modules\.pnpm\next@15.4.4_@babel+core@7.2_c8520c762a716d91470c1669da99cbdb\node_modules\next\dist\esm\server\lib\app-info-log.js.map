{"version": 3, "sources": ["../../../src/server/lib/app-info-log.ts"], "sourcesContent": ["import { loadEnvConfig } from '@next/env'\nimport * as Log from '../../build/output/log'\nimport { bold, purple } from '../../lib/picocolors'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport loadConfig, { type ConfiguredExperimentalFeature } from '../config'\n\nexport function logStartInfo({\n  networkUrl,\n  appUrl,\n  envInfo,\n  experimentalFeatures,\n  maxExperimentalFeatures = Infinity,\n}: {\n  networkUrl: string | null\n  appUrl: string | null\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n  maxExperimentalFeatures?: number\n}) {\n  let bundlerSuffix\n  if (process.env.TURBOPACK) {\n    bundlerSuffix = ' (Turbopack)'\n  } else if (process.env.NEXT_RSPACK) {\n    bundlerSuffix = ' (Rspack)'\n  } else {\n    bundlerSuffix = ''\n  }\n\n  Log.bootstrap(\n    `${bold(\n      purple(`${Log.prefixes.ready} Next.js ${process.env.__NEXT_VERSION}`)\n    )}${bundlerSuffix}`\n  )\n  if (appUrl) {\n    Log.bootstrap(`- Local:        ${appUrl}`)\n  }\n  if (networkUrl) {\n    Log.bootstrap(`- Network:      ${networkUrl}`)\n  }\n  if (envInfo?.length) Log.bootstrap(`- Environments: ${envInfo.join(', ')}`)\n\n  if (experimentalFeatures?.length) {\n    Log.bootstrap(`- Experiments (use with caution):`)\n    // only show a maximum number of flags\n    for (const exp of experimentalFeatures.slice(0, maxExperimentalFeatures)) {\n      const symbol =\n        typeof exp.value === 'boolean'\n          ? exp.value === true\n            ? bold('✓')\n            : bold('⨯')\n          : '·'\n\n      const suffix =\n        typeof exp.value === 'number' || typeof exp.value === 'string'\n          ? `: ${JSON.stringify(exp.value)}`\n          : ''\n\n      const reason = exp.reason ? ` (${exp.reason})` : ''\n\n      Log.bootstrap(`  ${symbol} ${exp.key}${suffix}${reason}`)\n    }\n    /* indicate if there are more than the maximum shown no. flags */\n    if (experimentalFeatures.length > maxExperimentalFeatures) {\n      Log.bootstrap(`  · ...`)\n    }\n  }\n\n  // New line after the bootstrap info\n  Log.info('')\n}\n\nexport async function getStartServerInfo({\n  dir,\n  dev,\n  debugPrerender,\n}: {\n  dir: string\n  dev: boolean\n  debugPrerender?: boolean\n}): Promise<{\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n}> {\n  let experimentalFeatures: ConfiguredExperimentalFeature[] = []\n  await loadConfig(\n    dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_BUILD,\n    dir,\n    {\n      reportExperimentalFeatures(features) {\n        experimentalFeatures = features.sort(\n          ({ key: a }, { key: b }) => a.length - b.length\n        )\n      },\n      debugPrerender,\n    }\n  )\n\n  // we need to reset env if we are going to create\n  // the worker process with the esm loader so that the\n  // initial env state is correct\n  let envInfo: string[] = []\n  const { loadedEnvFiles } = loadEnvConfig(dir, true, console, false)\n  if (loadedEnvFiles.length > 0) {\n    envInfo = loadedEnvFiles.map((f) => f.path)\n  }\n\n  return {\n    envInfo,\n    experimentalFeatures,\n  }\n}\n"], "names": ["loadEnvConfig", "Log", "bold", "purple", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "loadConfig", "logStartInfo", "networkUrl", "appUrl", "envInfo", "experimentalFeatures", "maxExperimentalFeatures", "Infinity", "bundlerSuffix", "process", "env", "TURBOPACK", "NEXT_RSPACK", "bootstrap", "prefixes", "ready", "__NEXT_VERSION", "length", "join", "exp", "slice", "symbol", "value", "suffix", "JSON", "stringify", "reason", "key", "info", "getStartServerInfo", "dir", "dev", "debugPrerender", "reportExperimentalFeatures", "features", "sort", "a", "b", "loadedEnvFiles", "console", "map", "f", "path"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAW;AACzC,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,IAAI,EAAEC,MAAM,QAAQ,uBAAsB;AACnD,SACEC,wBAAwB,EACxBC,sBAAsB,QACjB,6BAA4B;AACnC,OAAOC,gBAAwD,YAAW;AAE1E,OAAO,SAASC,aAAa,EAC3BC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,oBAAoB,EACpBC,0BAA0BC,QAAQ,EAOnC;IACC,IAAIC;IACJ,IAAIC,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzBH,gBAAgB;IAClB,OAAO,IAAIC,QAAQC,GAAG,CAACE,WAAW,EAAE;QAClCJ,gBAAgB;IAClB,OAAO;QACLA,gBAAgB;IAClB;IAEAb,IAAIkB,SAAS,CACX,GAAGjB,KACDC,OAAO,GAAGF,IAAImB,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAEN,QAAQC,GAAG,CAACM,cAAc,EAAE,KAClER,eAAe;IAErB,IAAIL,QAAQ;QACVR,IAAIkB,SAAS,CAAC,CAAC,gBAAgB,EAAEV,QAAQ;IAC3C;IACA,IAAID,YAAY;QACdP,IAAIkB,SAAS,CAAC,CAAC,gBAAgB,EAAEX,YAAY;IAC/C;IACA,IAAIE,2BAAAA,QAASa,MAAM,EAAEtB,IAAIkB,SAAS,CAAC,CAAC,gBAAgB,EAAET,QAAQc,IAAI,CAAC,OAAO;IAE1E,IAAIb,wCAAAA,qBAAsBY,MAAM,EAAE;QAChCtB,IAAIkB,SAAS,CAAC,CAAC,iCAAiC,CAAC;QACjD,sCAAsC;QACtC,KAAK,MAAMM,OAAOd,qBAAqBe,KAAK,CAAC,GAAGd,yBAA0B;YACxE,MAAMe,SACJ,OAAOF,IAAIG,KAAK,KAAK,YACjBH,IAAIG,KAAK,KAAK,OACZ1B,KAAK,OACLA,KAAK,OACP;YAEN,MAAM2B,SACJ,OAAOJ,IAAIG,KAAK,KAAK,YAAY,OAAOH,IAAIG,KAAK,KAAK,WAClD,CAAC,EAAE,EAAEE,KAAKC,SAAS,CAACN,IAAIG,KAAK,GAAG,GAChC;YAEN,MAAMI,SAASP,IAAIO,MAAM,GAAG,CAAC,EAAE,EAAEP,IAAIO,MAAM,CAAC,CAAC,CAAC,GAAG;YAEjD/B,IAAIkB,SAAS,CAAC,CAAC,EAAE,EAAEQ,OAAO,CAAC,EAAEF,IAAIQ,GAAG,GAAGJ,SAASG,QAAQ;QAC1D;QACA,+DAA+D,GAC/D,IAAIrB,qBAAqBY,MAAM,GAAGX,yBAAyB;YACzDX,IAAIkB,SAAS,CAAC,CAAC,OAAO,CAAC;QACzB;IACF;IAEA,oCAAoC;IACpClB,IAAIiC,IAAI,CAAC;AACX;AAEA,OAAO,eAAeC,mBAAmB,EACvCC,GAAG,EACHC,GAAG,EACHC,cAAc,EAKf;IAIC,IAAI3B,uBAAwD,EAAE;IAC9D,MAAML,WACJ+B,MAAMjC,2BAA2BC,wBACjC+B,KACA;QACEG,4BAA2BC,QAAQ;YACjC7B,uBAAuB6B,SAASC,IAAI,CAClC,CAAC,EAAER,KAAKS,CAAC,EAAE,EAAE,EAAET,KAAKU,CAAC,EAAE,GAAKD,EAAEnB,MAAM,GAAGoB,EAAEpB,MAAM;QAEnD;QACAe;IACF;IAGF,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAI5B,UAAoB,EAAE;IAC1B,MAAM,EAAEkC,cAAc,EAAE,GAAG5C,cAAcoC,KAAK,MAAMS,SAAS;IAC7D,IAAID,eAAerB,MAAM,GAAG,GAAG;QAC7Bb,UAAUkC,eAAeE,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI;IAC5C;IAEA,OAAO;QACLtC;QACAC;IACF;AACF", "ignoreList": [0]}