{"version": 3, "sources": ["../../src/server/next.ts"], "sourcesContent": ["import type { Options as DevServerOptions } from './dev/next-dev-server'\nimport type {\n  NodeRequestHandler,\n  Options as ServerOptions,\n} from './next-server'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { Duplex } from 'stream'\nimport type { NextUrlWithParsedQuery } from './request-meta'\n\nimport './require-hook'\nimport './node-polyfill-crypto'\n\nimport type { default as NextNodeServer } from './next-server'\nimport * as log from '../build/output/log'\nimport loadConfig from './config'\nimport path, { resolve } from 'path'\nimport { NON_STANDARD_NODE_ENV } from '../lib/constants'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  SERVER_FILES_MANIFEST,\n} from '../shared/lib/constants'\nimport { PHASE_PRODUCTION_SERVER } from '../shared/lib/constants'\nimport { getTracer } from './lib/trace/tracer'\nimport { NextServerSpan } from './lib/trace/constants'\nimport { formatUrl } from '../shared/lib/router/utils/format-url'\nimport type { ServerFields } from './lib/router-utils/setup-dev-bundler'\nimport type { ServerInitResult } from './lib/render-server'\nimport { AsyncCallbackSet } from './lib/async-callback-set'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n} from './lib/router-utils/router-server-context'\n\nlet ServerImpl: typeof NextNodeServer\n\nconst getServerImpl = async () => {\n  if (ServerImpl === undefined) {\n    ServerImpl = (\n      await Promise.resolve(\n        require('./next-server') as typeof import('./next-server')\n      )\n    ).default\n  }\n  return ServerImpl\n}\n\nexport type NextServerOptions = Omit<\n  ServerOptions | DevServerOptions,\n  // This is assigned in this server abstraction.\n  'conf'\n> &\n  Partial<Pick<ServerOptions | DevServerOptions, 'conf'>>\n\nexport type RequestHandler = (\n  req: IncomingMessage,\n  res: ServerResponse,\n  parsedUrl?: NextUrlWithParsedQuery | undefined\n) => Promise<void>\n\nexport type UpgradeHandler = (\n  req: IncomingMessage,\n  socket: Duplex,\n  head: Buffer\n) => Promise<void>\n\nconst SYMBOL_LOAD_CONFIG = Symbol('next.load_config')\n\ninterface NextWrapperServer {\n  // NOTE: the methods/properties here are the public API for custom servers.\n  // Consider backwards compatibilty when changing something here!\n\n  options: NextServerOptions\n  hostname: string | undefined\n  port: number | undefined\n\n  getRequestHandler(): RequestHandler\n  prepare(serverFields?: ServerFields): Promise<void>\n  setAssetPrefix(assetPrefix: string): void\n  close(): Promise<void>\n\n  // used internally\n  getUpgradeHandler(): UpgradeHandler\n\n  // legacy methods that we left exposed in the past\n\n  logError(...args: Parameters<NextNodeServer['logError']>): void\n\n  revalidate(\n    ...args: Parameters<NextNodeServer['revalidate']>\n  ): ReturnType<NextNodeServer['revalidate']>\n\n  logErrorWithOriginalStack(err: unknown, type: string): void\n\n  render(\n    ...args: Parameters<NextNodeServer['render']>\n  ): ReturnType<NextNodeServer['render']>\n\n  renderToHTML(\n    ...args: Parameters<NextNodeServer['renderToHTML']>\n  ): ReturnType<NextNodeServer['renderToHTML']>\n\n  renderError(\n    ...args: Parameters<NextNodeServer['renderError']>\n  ): ReturnType<NextNodeServer['renderError']>\n\n  renderErrorToHTML(\n    ...args: Parameters<NextNodeServer['renderErrorToHTML']>\n  ): ReturnType<NextNodeServer['renderErrorToHTML']>\n\n  render404(\n    ...args: Parameters<NextNodeServer['render404']>\n  ): ReturnType<NextNodeServer['render404']>\n}\n\n/** The wrapper server used by `next start` */\nexport class NextServer implements NextWrapperServer {\n  private serverPromise?: Promise<NextNodeServer>\n  private server?: NextNodeServer\n  private reqHandler?: NodeRequestHandler\n  private reqHandlerPromise?: Promise<NodeRequestHandler>\n  private preparedAssetPrefix?: string\n\n  public options: NextServerOptions\n\n  constructor(options: NextServerOptions) {\n    this.options = options\n  }\n\n  get hostname() {\n    return this.options.hostname\n  }\n\n  get port() {\n    return this.options.port\n  }\n\n  getRequestHandler(): RequestHandler {\n    return async (\n      req: IncomingMessage,\n      res: ServerResponse,\n      parsedUrl?: UrlWithParsedQuery\n    ) => {\n      return getTracer().trace(NextServerSpan.getRequestHandler, async () => {\n        const requestHandler = await this.getServerRequestHandler()\n        return requestHandler(req, res, parsedUrl)\n      })\n    }\n  }\n\n  getUpgradeHandler(): UpgradeHandler {\n    return async (req: IncomingMessage, socket: any, head: any) => {\n      const server = await this.getServer()\n      // @ts-expect-error we mark this as protected so it\n      // causes an error here\n      return server.handleUpgrade.apply(server, [req, socket, head])\n    }\n  }\n\n  setAssetPrefix(assetPrefix: string) {\n    if (this.server) {\n      this.server.setAssetPrefix(assetPrefix)\n    } else {\n      this.preparedAssetPrefix = assetPrefix\n    }\n  }\n\n  logError(...args: Parameters<NextWrapperServer['logError']>) {\n    if (this.server) {\n      this.server.logError(...args)\n    }\n  }\n\n  async logErrorWithOriginalStack(err: unknown, type: string) {\n    const server = await this.getServer()\n    // this is only available on dev server\n    if ((server as any).logErrorWithOriginalStack) {\n      return (server as any).logErrorWithOriginalStack(err, type)\n    }\n  }\n\n  async revalidate(...args: Parameters<NextWrapperServer['revalidate']>) {\n    const server = await this.getServer()\n    return server.revalidate(...args)\n  }\n\n  async render(...args: Parameters<NextWrapperServer['render']>) {\n    const server = await this.getServer()\n    return server.render(...args)\n  }\n\n  async renderToHTML(...args: Parameters<NextWrapperServer['renderToHTML']>) {\n    const server = await this.getServer()\n    return server.renderToHTML(...args)\n  }\n\n  async renderError(...args: Parameters<NextWrapperServer['renderError']>) {\n    const server = await this.getServer()\n    return server.renderError(...args)\n  }\n\n  async renderErrorToHTML(\n    ...args: Parameters<NextWrapperServer['renderErrorToHTML']>\n  ) {\n    const server = await this.getServer()\n    return server.renderErrorToHTML(...args)\n  }\n\n  async render404(...args: Parameters<NextWrapperServer['render404']>) {\n    const server = await this.getServer()\n    return server.render404(...args)\n  }\n\n  async prepare(serverFields?: ServerFields) {\n    const server = await this.getServer()\n\n    if (serverFields) {\n      Object.assign(server, serverFields)\n    }\n    // We shouldn't prepare the server in production,\n    // because this code won't be executed when deployed\n    if (this.options.dev) {\n      await server.prepare()\n    }\n  }\n\n  async close() {\n    if (this.server) {\n      await this.server.close()\n    }\n  }\n\n  private async createServer(\n    options: ServerOptions | DevServerOptions\n  ): Promise<NextNodeServer> {\n    let ServerImplementation: typeof NextNodeServer\n    if (options.dev) {\n      ServerImplementation = (\n        require('./dev/next-dev-server') as typeof import('./dev/next-dev-server')\n      ).default as typeof import('./dev/next-dev-server').default\n    } else {\n      ServerImplementation = await getServerImpl()\n    }\n    const server = new ServerImplementation(options)\n\n    return server\n  }\n\n  private async [SYMBOL_LOAD_CONFIG]() {\n    const dir = resolve(this.options.dir || '.')\n\n    const config = await loadConfig(\n      this.options.dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_SERVER,\n      dir,\n      {\n        customConfig: this.options.conf,\n        silent: true,\n      }\n    )\n\n    // check serialized build config when available\n    if (!this.options.dev) {\n      try {\n        const serializedConfig = require(\n          path.join(dir, config.distDir, SERVER_FILES_MANIFEST)\n        ).config\n\n        config.experimental.isExperimentalCompile =\n          serializedConfig.experimental.isExperimentalCompile\n      } catch (_) {\n        // if distDir is customized we don't know until we\n        // load the config so fallback to loading the config\n        // from next.config.js\n      }\n    }\n\n    return config\n  }\n\n  private async getServer() {\n    if (!this.serverPromise) {\n      this.serverPromise = this[SYMBOL_LOAD_CONFIG]().then(async (conf) => {\n        if (!this.options.dev) {\n          if (conf.output === 'standalone') {\n            if (!process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n              log.warn(\n                `\"next start\" does not work with \"output: standalone\" configuration. Use \"node .next/standalone/server.js\" instead.`\n              )\n            }\n          } else if (conf.output === 'export') {\n            throw new Error(\n              `\"next start\" does not work with \"output: export\" configuration. Use \"npx serve@latest out\" instead.`\n            )\n          }\n        }\n\n        this.server = await this.createServer({\n          ...this.options,\n          conf,\n        })\n        if (this.preparedAssetPrefix) {\n          this.server.setAssetPrefix(this.preparedAssetPrefix)\n        }\n        return this.server\n      })\n    }\n    return this.serverPromise\n  }\n\n  private async getServerRequestHandler() {\n    if (this.reqHandler) return this.reqHandler\n\n    // Memoize request handler creation\n    if (!this.reqHandlerPromise) {\n      this.reqHandlerPromise = this.getServer().then((server) => {\n        this.reqHandler = getTracer().wrap(\n          NextServerSpan.getServerRequestHandler,\n          server.getRequestHandler().bind(server)\n        )\n        delete this.reqHandlerPromise\n        return this.reqHandler\n      })\n    }\n    return this.reqHandlerPromise\n  }\n}\n\n/** The wrapper server used for `import next from \"next\" (in a custom server)` */\nclass NextCustomServer implements NextWrapperServer {\n  private didWebSocketSetup: boolean = false\n  protected cleanupListeners?: AsyncCallbackSet\n\n  protected init?: ServerInitResult\n\n  public options: NextServerOptions\n\n  constructor(options: NextServerOptions) {\n    this.options = options\n  }\n\n  protected getInit() {\n    if (!this.init) {\n      throw new Error(\n        'prepare() must be called before performing this operation'\n      )\n    }\n    return this.init\n  }\n\n  protected get requestHandler() {\n    return this.getInit().requestHandler\n  }\n  protected get upgradeHandler() {\n    return this.getInit().upgradeHandler\n  }\n  protected get server() {\n    return this.getInit().server\n  }\n\n  get hostname() {\n    return this.options.hostname\n  }\n\n  get port() {\n    return this.options.port\n  }\n\n  async prepare() {\n    const { getRequestHandlers } =\n      require('./lib/start-server') as typeof import('./lib/start-server')\n\n    let onDevServerCleanup: AsyncCallbackSet['add'] | undefined\n    if (this.options.dev) {\n      this.cleanupListeners = new AsyncCallbackSet()\n      onDevServerCleanup = this.cleanupListeners.add.bind(this.cleanupListeners)\n    }\n\n    const initResult = await getRequestHandlers({\n      dir: this.options.dir!,\n      port: this.options.port || 3000,\n      isDev: !!this.options.dev,\n      onDevServerCleanup,\n      hostname: this.options.hostname || 'localhost',\n      minimalMode: this.options.minimalMode,\n      quiet: this.options.quiet,\n    })\n    this.init = initResult\n  }\n\n  private setupWebSocketHandler(\n    customServer?: import('http').Server,\n    _req?: IncomingMessage\n  ) {\n    if (!this.didWebSocketSetup) {\n      this.didWebSocketSetup = true\n      customServer = customServer || (_req?.socket as any)?.server\n\n      if (customServer) {\n        customServer.on('upgrade', async (req, socket, head) => {\n          this.upgradeHandler(req, socket, head)\n        })\n      }\n    }\n  }\n\n  getRequestHandler(): RequestHandler {\n    return async (\n      req: IncomingMessage,\n      res: ServerResponse,\n      parsedUrl?: UrlWithParsedQuery\n    ) => {\n      this.setupWebSocketHandler(this.options.httpServer, req)\n\n      if (parsedUrl) {\n        req.url = formatUrl(parsedUrl)\n      }\n\n      return this.requestHandler(req, res)\n    }\n  }\n\n  async render(...args: Parameters<NextWrapperServer['render']>) {\n    let [req, res, pathname, query, parsedUrl] = args\n    this.setupWebSocketHandler(this.options.httpServer, req as IncomingMessage)\n\n    if (!pathname.startsWith('/')) {\n      console.error(`Cannot render page with path \"${pathname}\"`)\n      pathname = `/${pathname}`\n    }\n    pathname = pathname === '/index' ? '/' : pathname\n\n    req.url = formatUrl({\n      ...parsedUrl,\n      pathname,\n      query,\n    })\n\n    await this.requestHandler(req as IncomingMessage, res as ServerResponse)\n    return\n  }\n\n  setAssetPrefix(assetPrefix: string): void {\n    this.server.setAssetPrefix(assetPrefix)\n\n    // update the router-server nextConfig instance as\n    // this is the source of truth for \"handler\" in serverful\n    const relativeProjectDir = path.relative(\n      process.cwd(),\n      this.options.dir || ''\n    )\n\n    if (\n      routerServerGlobal[RouterServerContextSymbol]?.[relativeProjectDir]\n        ?.nextConfig\n    ) {\n      routerServerGlobal[RouterServerContextSymbol][\n        relativeProjectDir\n      ].nextConfig.assetPrefix = assetPrefix\n    }\n  }\n\n  getUpgradeHandler(): UpgradeHandler {\n    return this.server.getUpgradeHandler()\n  }\n\n  logError(...args: Parameters<NextWrapperServer['logError']>) {\n    this.server.logError(...args)\n  }\n\n  logErrorWithOriginalStack(err: unknown, type: string) {\n    return this.server.logErrorWithOriginalStack(err, type)\n  }\n\n  async revalidate(...args: Parameters<NextWrapperServer['revalidate']>) {\n    return this.server.revalidate(...args)\n  }\n\n  async renderToHTML(...args: Parameters<NextWrapperServer['renderToHTML']>) {\n    return this.server.renderToHTML(...args)\n  }\n\n  async renderError(...args: Parameters<NextWrapperServer['renderError']>) {\n    return this.server.renderError(...args)\n  }\n\n  async renderErrorToHTML(\n    ...args: Parameters<NextWrapperServer['renderErrorToHTML']>\n  ) {\n    return this.server.renderErrorToHTML(...args)\n  }\n\n  async render404(...args: Parameters<NextWrapperServer['render404']>) {\n    return this.server.render404(...args)\n  }\n\n  async close() {\n    await Promise.allSettled([\n      this.init?.server.close(),\n      this.cleanupListeners?.runAll(),\n    ])\n  }\n}\n\n// This file is used for when users run `require('next')`\nfunction createServer(\n  options: NextServerOptions & {\n    turbo?: boolean\n    turbopack?: boolean\n  }\n): NextWrapperServer {\n  if (\n    options &&\n    (options.turbo || options.turbopack || process.env.IS_TURBOPACK_TEST)\n  ) {\n    process.env.TURBOPACK = '1'\n  }\n  // The package is used as a TypeScript plugin.\n  if (\n    options &&\n    'typescript' in options &&\n    'version' in (options as any).typescript\n  ) {\n    const pluginMod: typeof import('./next-typescript') =\n      require('./next-typescript') as typeof import('./next-typescript')\n    return pluginMod.createTSPlugin(\n      options as any\n    ) as unknown as NextWrapperServer\n  }\n\n  if (options == null) {\n    throw new Error(\n      'The server has not been instantiated properly. https://nextjs.org/docs/messages/invalid-server-options'\n    )\n  }\n\n  if (\n    !('isNextDevCommand' in options) &&\n    process.env.NODE_ENV &&\n    !['production', 'development', 'test'].includes(process.env.NODE_ENV)\n  ) {\n    log.warn(NON_STANDARD_NODE_ENV)\n  }\n\n  if (options.dev && typeof options.dev !== 'boolean') {\n    console.warn(\n      \"Warning: 'dev' is not a boolean which could introduce unexpected behavior. https://nextjs.org/docs/messages/invalid-server-options\"\n    )\n  }\n\n  // When the caller is a custom server (using next()).\n  if (options.customServer !== false) {\n    const dir = resolve(options.dir || '.')\n\n    return new NextCustomServer({\n      ...options,\n      dir,\n    })\n  }\n\n  // When the caller is Next.js internals (i.e. render worker, start server, etc)\n  return new NextServer(options)\n}\n\n// Support commonjs `require('next')`\nmodule.exports = createServer\n// exports = module.exports\n\n// Support `import next from 'next'`\nexport default createServer\n"], "names": ["log", "loadConfig", "path", "resolve", "NON_STANDARD_NODE_ENV", "PHASE_DEVELOPMENT_SERVER", "SERVER_FILES_MANIFEST", "PHASE_PRODUCTION_SERVER", "getTracer", "NextServerSpan", "formatUrl", "AsyncCallbackSet", "RouterServerContextSymbol", "routerServerGlobal", "ServerImpl", "getServerImpl", "undefined", "Promise", "require", "default", "SYMBOL_LOAD_CONFIG", "Symbol", "NextServer", "constructor", "options", "hostname", "port", "getRequestHandler", "req", "res", "parsedUrl", "trace", "requestHandler", "getServerRequestHandler", "getUpgradeHandler", "socket", "head", "server", "getServer", "handleUpgrade", "apply", "setAssetPrefix", "assetPrefix", "preparedAssetPrefix", "logError", "args", "logErrorWithOriginalStack", "err", "type", "revalidate", "render", "renderToHTML", "renderError", "renderErrorToHTML", "render404", "prepare", "serverFields", "Object", "assign", "dev", "close", "createServer", "ServerImplementation", "dir", "config", "customConfig", "conf", "silent", "serializedConfig", "join", "distDir", "experimental", "isExperimentalCompile", "_", "serverPromise", "then", "output", "process", "env", "__NEXT_PRIVATE_STANDALONE_CONFIG", "warn", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reqHandlerPromise", "wrap", "bind", "NextCustomServer", "didWebSocketSetup", "getInit", "init", "upgradeHandler", "getRequestHandlers", "onDevServerCleanup", "cleanupListeners", "add", "initResult", "isDev", "minimalMode", "quiet", "setupWebSocketHandler", "customServer", "_req", "on", "httpServer", "url", "pathname", "query", "startsWith", "console", "error", "relativeProjectDir", "relative", "cwd", "nextConfig", "allSettled", "runAll", "turbo", "turbopack", "IS_TURBOPACK_TEST", "TURBOPACK", "typescript", "pluginMod", "createTSPlugin", "NODE_ENV", "includes", "module", "exports"], "mappings": "AAUA,OAAO,iBAAgB;AACvB,OAAO,yBAAwB;AAG/B,YAAYA,SAAS,sBAAqB;AAC1C,OAAOC,gBAAgB,WAAU;AACjC,OAAOC,QAAQC,OAAO,QAAQ,OAAM;AACpC,SAASC,qBAAqB,QAAQ,mBAAkB;AACxD,SACEC,wBAAwB,EACxBC,qBAAqB,QAChB,0BAAyB;AAChC,SAASC,uBAAuB,QAAQ,0BAAyB;AACjE,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,SAAS,QAAQ,wCAAuC;AAGjE,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SACEC,yBAAyB,EACzBC,kBAAkB,QACb,2CAA0C;AAEjD,IAAIC;AAEJ,MAAMC,gBAAgB;IACpB,IAAID,eAAeE,WAAW;QAC5BF,aAAa,AACX,CAAA,MAAMG,QAAQd,OAAO,CACnBe,QAAQ,iBACV,EACAC,OAAO;IACX;IACA,OAAOL;AACT;AAqBA,MAAMM,qBAAqBC,OAAO;AAiDlC,4CAA4C,GAC5C,OAAO,MAAMC;IASXC,YAAYC,OAA0B,CAAE;QACtC,IAAI,CAACA,OAAO,GAAGA;IACjB;IAEA,IAAIC,WAAW;QACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ;IAC9B;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI;IAC1B;IAEAC,oBAAoC;QAClC,OAAO,OACLC,KACAC,KACAC;YAEA,OAAOtB,YAAYuB,KAAK,CAACtB,eAAekB,iBAAiB,EAAE;gBACzD,MAAMK,iBAAiB,MAAM,IAAI,CAACC,uBAAuB;gBACzD,OAAOD,eAAeJ,KAAKC,KAAKC;YAClC;QACF;IACF;IAEAI,oBAAoC;QAClC,OAAO,OAAON,KAAsBO,QAAaC;YAC/C,MAAMC,SAAS,MAAM,IAAI,CAACC,SAAS;YACnC,mDAAmD;YACnD,uBAAuB;YACvB,OAAOD,OAAOE,aAAa,CAACC,KAAK,CAACH,QAAQ;gBAACT;gBAAKO;gBAAQC;aAAK;QAC/D;IACF;IAEAK,eAAeC,WAAmB,EAAE;QAClC,IAAI,IAAI,CAACL,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACI,cAAc,CAACC;QAC7B,OAAO;YACL,IAAI,CAACC,mBAAmB,GAAGD;QAC7B;IACF;IAEAE,SAAS,GAAGC,IAA+C,EAAE;QAC3D,IAAI,IAAI,CAACR,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACO,QAAQ,IAAIC;QAC1B;IACF;IAEA,MAAMC,0BAA0BC,GAAY,EAAEC,IAAY,EAAE;QAC1D,MAAMX,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,uCAAuC;QACvC,IAAI,AAACD,OAAeS,yBAAyB,EAAE;YAC7C,OAAO,AAACT,OAAeS,yBAAyB,CAACC,KAAKC;QACxD;IACF;IAEA,MAAMC,WAAW,GAAGJ,IAAiD,EAAE;QACrE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOY,UAAU,IAAIJ;IAC9B;IAEA,MAAMK,OAAO,GAAGL,IAA6C,EAAE;QAC7D,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOa,MAAM,IAAIL;IAC1B;IAEA,MAAMM,aAAa,GAAGN,IAAmD,EAAE;QACzE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOc,YAAY,IAAIN;IAChC;IAEA,MAAMO,YAAY,GAAGP,IAAkD,EAAE;QACvE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOe,WAAW,IAAIP;IAC/B;IAEA,MAAMQ,kBACJ,GAAGR,IAAwD,EAC3D;QACA,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOgB,iBAAiB,IAAIR;IACrC;IAEA,MAAMS,UAAU,GAAGT,IAAgD,EAAE;QACnE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOiB,SAAS,IAAIT;IAC7B;IAEA,MAAMU,QAAQC,YAA2B,EAAE;QACzC,MAAMnB,SAAS,MAAM,IAAI,CAACC,SAAS;QAEnC,IAAIkB,cAAc;YAChBC,OAAOC,MAAM,CAACrB,QAAQmB;QACxB;QACA,iDAAiD;QACjD,oDAAoD;QACpD,IAAI,IAAI,CAAChC,OAAO,CAACmC,GAAG,EAAE;YACpB,MAAMtB,OAAOkB,OAAO;QACtB;IACF;IAEA,MAAMK,QAAQ;QACZ,IAAI,IAAI,CAACvB,MAAM,EAAE;YACf,MAAM,IAAI,CAACA,MAAM,CAACuB,KAAK;QACzB;IACF;IAEA,MAAcC,aACZrC,OAAyC,EAChB;QACzB,IAAIsC;QACJ,IAAItC,QAAQmC,GAAG,EAAE;YACfG,uBAAuB,AACrB5C,QAAQ,yBACRC,OAAO;QACX,OAAO;YACL2C,uBAAuB,MAAM/C;QAC/B;QACA,MAAMsB,SAAS,IAAIyB,qBAAqBtC;QAExC,OAAOa;IACT;IAEA,MAAc,CAACjB,mBAAmB,GAAG;QACnC,MAAM2C,MAAM5D,QAAQ,IAAI,CAACqB,OAAO,CAACuC,GAAG,IAAI;QAExC,MAAMC,SAAS,MAAM/D,WACnB,IAAI,CAACuB,OAAO,CAACmC,GAAG,GAAGtD,2BAA2BE,yBAC9CwD,KACA;YACEE,cAAc,IAAI,CAACzC,OAAO,CAAC0C,IAAI;YAC/BC,QAAQ;QACV;QAGF,+CAA+C;QAC/C,IAAI,CAAC,IAAI,CAAC3C,OAAO,CAACmC,GAAG,EAAE;YACrB,IAAI;gBACF,MAAMS,mBAAmBlD,QACvBhB,KAAKmE,IAAI,CAACN,KAAKC,OAAOM,OAAO,EAAEhE,wBAC/B0D,MAAM;gBAERA,OAAOO,YAAY,CAACC,qBAAqB,GACvCJ,iBAAiBG,YAAY,CAACC,qBAAqB;YACvD,EAAE,OAAOC,GAAG;YACV,kDAAkD;YAClD,oDAAoD;YACpD,sBAAsB;YACxB;QACF;QAEA,OAAOT;IACT;IAEA,MAAc1B,YAAY;QACxB,IAAI,CAAC,IAAI,CAACoC,aAAa,EAAE;YACvB,IAAI,CAACA,aAAa,GAAG,IAAI,CAACtD,mBAAmB,GAAGuD,IAAI,CAAC,OAAOT;gBAC1D,IAAI,CAAC,IAAI,CAAC1C,OAAO,CAACmC,GAAG,EAAE;oBACrB,IAAIO,KAAKU,MAAM,KAAK,cAAc;wBAChC,IAAI,CAACC,QAAQC,GAAG,CAACC,gCAAgC,EAAE;4BACjD/E,IAAIgF,IAAI,CACN,CAAC,kHAAkH,CAAC;wBAExH;oBACF,OAAO,IAAId,KAAKU,MAAM,KAAK,UAAU;wBACnC,MAAM,qBAEL,CAFK,IAAIK,MACR,CAAC,mGAAmG,CAAC,GADjG,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEA,IAAI,CAAC5C,MAAM,GAAG,MAAM,IAAI,CAACwB,YAAY,CAAC;oBACpC,GAAG,IAAI,CAACrC,OAAO;oBACf0C;gBACF;gBACA,IAAI,IAAI,CAACvB,mBAAmB,EAAE;oBAC5B,IAAI,CAACN,MAAM,CAACI,cAAc,CAAC,IAAI,CAACE,mBAAmB;gBACrD;gBACA,OAAO,IAAI,CAACN,MAAM;YACpB;QACF;QACA,OAAO,IAAI,CAACqC,aAAa;IAC3B;IAEA,MAAczC,0BAA0B;QACtC,IAAI,IAAI,CAACiD,UAAU,EAAE,OAAO,IAAI,CAACA,UAAU;QAE3C,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;YAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAAC7C,SAAS,GAAGqC,IAAI,CAAC,CAACtC;gBAC9C,IAAI,CAAC6C,UAAU,GAAG1E,YAAY4E,IAAI,CAChC3E,eAAewB,uBAAuB,EACtCI,OAAOV,iBAAiB,GAAG0D,IAAI,CAAChD;gBAElC,OAAO,IAAI,CAAC8C,iBAAiB;gBAC7B,OAAO,IAAI,CAACD,UAAU;YACxB;QACF;QACA,OAAO,IAAI,CAACC,iBAAiB;IAC/B;AACF;AAEA,+EAA+E,GAC/E,MAAMG;IAQJ/D,YAAYC,OAA0B,CAAE;aAPhC+D,oBAA6B;QAQnC,IAAI,CAAC/D,OAAO,GAAGA;IACjB;IAEUgE,UAAU;QAClB,IAAI,CAAC,IAAI,CAACC,IAAI,EAAE;YACd,MAAM,qBAEL,CAFK,IAAIR,MACR,8DADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAO,IAAI,CAACQ,IAAI;IAClB;IAEA,IAAczD,iBAAiB;QAC7B,OAAO,IAAI,CAACwD,OAAO,GAAGxD,cAAc;IACtC;IACA,IAAc0D,iBAAiB;QAC7B,OAAO,IAAI,CAACF,OAAO,GAAGE,cAAc;IACtC;IACA,IAAcrD,SAAS;QACrB,OAAO,IAAI,CAACmD,OAAO,GAAGnD,MAAM;IAC9B;IAEA,IAAIZ,WAAW;QACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ;IAC9B;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI;IAC1B;IAEA,MAAM6B,UAAU;QACd,MAAM,EAAEoC,kBAAkB,EAAE,GAC1BzE,QAAQ;QAEV,IAAI0E;QACJ,IAAI,IAAI,CAACpE,OAAO,CAACmC,GAAG,EAAE;YACpB,IAAI,CAACkC,gBAAgB,GAAG,IAAIlF;YAC5BiF,qBAAqB,IAAI,CAACC,gBAAgB,CAACC,GAAG,CAACT,IAAI,CAAC,IAAI,CAACQ,gBAAgB;QAC3E;QAEA,MAAME,aAAa,MAAMJ,mBAAmB;YAC1C5B,KAAK,IAAI,CAACvC,OAAO,CAACuC,GAAG;YACrBrC,MAAM,IAAI,CAACF,OAAO,CAACE,IAAI,IAAI;YAC3BsE,OAAO,CAAC,CAAC,IAAI,CAACxE,OAAO,CAACmC,GAAG;YACzBiC;YACAnE,UAAU,IAAI,CAACD,OAAO,CAACC,QAAQ,IAAI;YACnCwE,aAAa,IAAI,CAACzE,OAAO,CAACyE,WAAW;YACrCC,OAAO,IAAI,CAAC1E,OAAO,CAAC0E,KAAK;QAC3B;QACA,IAAI,CAACT,IAAI,GAAGM;IACd;IAEQI,sBACNC,YAAoC,EACpCC,IAAsB,EACtB;QACA,IAAI,CAAC,IAAI,CAACd,iBAAiB,EAAE;gBAEKc;YADhC,IAAI,CAACd,iBAAiB,GAAG;YACzBa,eAAeA,iBAAiBC,yBAAAA,cAAAA,KAAMlE,MAAM,qBAAb,AAACkE,YAAsBhE,MAAM;YAE5D,IAAI+D,cAAc;gBAChBA,aAAaE,EAAE,CAAC,WAAW,OAAO1E,KAAKO,QAAQC;oBAC7C,IAAI,CAACsD,cAAc,CAAC9D,KAAKO,QAAQC;gBACnC;YACF;QACF;IACF;IAEAT,oBAAoC;QAClC,OAAO,OACLC,KACAC,KACAC;YAEA,IAAI,CAACqE,qBAAqB,CAAC,IAAI,CAAC3E,OAAO,CAAC+E,UAAU,EAAE3E;YAEpD,IAAIE,WAAW;gBACbF,IAAI4E,GAAG,GAAG9F,UAAUoB;YACtB;YAEA,OAAO,IAAI,CAACE,cAAc,CAACJ,KAAKC;QAClC;IACF;IAEA,MAAMqB,OAAO,GAAGL,IAA6C,EAAE;QAC7D,IAAI,CAACjB,KAAKC,KAAK4E,UAAUC,OAAO5E,UAAU,GAAGe;QAC7C,IAAI,CAACsD,qBAAqB,CAAC,IAAI,CAAC3E,OAAO,CAAC+E,UAAU,EAAE3E;QAEpD,IAAI,CAAC6E,SAASE,UAAU,CAAC,MAAM;YAC7BC,QAAQC,KAAK,CAAC,CAAC,8BAA8B,EAAEJ,SAAS,CAAC,CAAC;YAC1DA,WAAW,CAAC,CAAC,EAAEA,UAAU;QAC3B;QACAA,WAAWA,aAAa,WAAW,MAAMA;QAEzC7E,IAAI4E,GAAG,GAAG9F,UAAU;YAClB,GAAGoB,SAAS;YACZ2E;YACAC;QACF;QAEA,MAAM,IAAI,CAAC1E,cAAc,CAACJ,KAAwBC;QAClD;IACF;IAEAY,eAAeC,WAAmB,EAAQ;YAWtC7B,kEAAAA;QAVF,IAAI,CAACwB,MAAM,CAACI,cAAc,CAACC;QAE3B,kDAAkD;QAClD,yDAAyD;QACzD,MAAMoE,qBAAqB5G,KAAK6G,QAAQ,CACtClC,QAAQmC,GAAG,IACX,IAAI,CAACxF,OAAO,CAACuC,GAAG,IAAI;QAGtB,KACElD,gDAAAA,kBAAkB,CAACD,0BAA0B,sBAA7CC,mEAAAA,6CAA+C,CAACiG,mBAAmB,qBAAnEjG,iEACIoG,UAAU,EACd;YACApG,kBAAkB,CAACD,0BAA0B,CAC3CkG,mBACD,CAACG,UAAU,CAACvE,WAAW,GAAGA;QAC7B;IACF;IAEAR,oBAAoC;QAClC,OAAO,IAAI,CAACG,MAAM,CAACH,iBAAiB;IACtC;IAEAU,SAAS,GAAGC,IAA+C,EAAE;QAC3D,IAAI,CAACR,MAAM,CAACO,QAAQ,IAAIC;IAC1B;IAEAC,0BAA0BC,GAAY,EAAEC,IAAY,EAAE;QACpD,OAAO,IAAI,CAACX,MAAM,CAACS,yBAAyB,CAACC,KAAKC;IACpD;IAEA,MAAMC,WAAW,GAAGJ,IAAiD,EAAE;QACrE,OAAO,IAAI,CAACR,MAAM,CAACY,UAAU,IAAIJ;IACnC;IAEA,MAAMM,aAAa,GAAGN,IAAmD,EAAE;QACzE,OAAO,IAAI,CAACR,MAAM,CAACc,YAAY,IAAIN;IACrC;IAEA,MAAMO,YAAY,GAAGP,IAAkD,EAAE;QACvE,OAAO,IAAI,CAACR,MAAM,CAACe,WAAW,IAAIP;IACpC;IAEA,MAAMQ,kBACJ,GAAGR,IAAwD,EAC3D;QACA,OAAO,IAAI,CAACR,MAAM,CAACgB,iBAAiB,IAAIR;IAC1C;IAEA,MAAMS,UAAU,GAAGT,IAAgD,EAAE;QACnE,OAAO,IAAI,CAACR,MAAM,CAACiB,SAAS,IAAIT;IAClC;IAEA,MAAMe,QAAQ;YAEV,YACA;QAFF,MAAM3C,QAAQiG,UAAU,CAAC;aACvB,aAAA,IAAI,CAACzB,IAAI,qBAAT,WAAWpD,MAAM,CAACuB,KAAK;aACvB,yBAAA,IAAI,CAACiC,gBAAgB,qBAArB,uBAAuBsB,MAAM;SAC9B;IACH;AACF;AAEA,yDAAyD;AACzD,SAAStD,aACPrC,OAGC;IAED,IACEA,WACCA,CAAAA,QAAQ4F,KAAK,IAAI5F,QAAQ6F,SAAS,IAAIxC,QAAQC,GAAG,CAACwC,iBAAiB,AAAD,GACnE;QACAzC,QAAQC,GAAG,CAACyC,SAAS,GAAG;IAC1B;IACA,8CAA8C;IAC9C,IACE/F,WACA,gBAAgBA,WAChB,aAAa,AAACA,QAAgBgG,UAAU,EACxC;QACA,MAAMC,YACJvG,QAAQ;QACV,OAAOuG,UAAUC,cAAc,CAC7BlG;IAEJ;IAEA,IAAIA,WAAW,MAAM;QACnB,MAAM,qBAEL,CAFK,IAAIyD,MACR,2GADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IACE,CAAE,CAAA,sBAAsBzD,OAAM,KAC9BqD,QAAQC,GAAG,CAAC6C,QAAQ,IACpB,CAAC;QAAC;QAAc;QAAe;KAAO,CAACC,QAAQ,CAAC/C,QAAQC,GAAG,CAAC6C,QAAQ,GACpE;QACA3H,IAAIgF,IAAI,CAAC5E;IACX;IAEA,IAAIoB,QAAQmC,GAAG,IAAI,OAAOnC,QAAQmC,GAAG,KAAK,WAAW;QACnDiD,QAAQ5B,IAAI,CACV;IAEJ;IAEA,qDAAqD;IACrD,IAAIxD,QAAQ4E,YAAY,KAAK,OAAO;QAClC,MAAMrC,MAAM5D,QAAQqB,QAAQuC,GAAG,IAAI;QAEnC,OAAO,IAAIuB,iBAAiB;YAC1B,GAAG9D,OAAO;YACVuC;QACF;IACF;IAEA,+EAA+E;IAC/E,OAAO,IAAIzC,WAAWE;AACxB;AAEA,qCAAqC;AACrCqG,OAAOC,OAAO,GAAGjE;AACjB,2BAA2B;AAE3B,oCAAoC;AACpC,eAAeA,aAAY", "ignoreList": [0]}