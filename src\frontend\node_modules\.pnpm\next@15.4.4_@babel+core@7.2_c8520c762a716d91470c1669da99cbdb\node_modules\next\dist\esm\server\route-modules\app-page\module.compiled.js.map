{"version": 3, "sources": ["../../../../src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCC,OAAOC,OAAO,GAAGC,QAAQ;AAC3B,OAAO;IACL,IAAIL,QAAQC,GAAG,CAACK,yBAAyB,EAAE;QACzC,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,eAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,EAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;gBACLF,OAAOC,OAAO,GAAGC,QAAQ;YAC3B;QACF,OAAO;YACL,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;gBACLF,OAAOC,OAAO,GAAGC,QAAQ;YAC3B;QACF;IACF,OAAO;QACL,IAAIL,QAAQC,GAAG,CAACM,QAAQ,KAAK,eAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,EAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;gBACLF,OAAOC,OAAO,GAAGC,QAAQ;YAC3B;QACF,OAAO;YACL,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;gBACLF,OAAOC,OAAO,GAAGC,QAAQ;YAC3B;QACF;IACF;AACF", "ignoreList": [0]}