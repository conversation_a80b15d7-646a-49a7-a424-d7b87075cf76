{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "sourcesContent": ["import type { IncrementalCache } from '../../lib/incremental-cache'\n\nimport { CACHE_ONE_YEAR } from '../../../lib/constants'\nimport { validateRevalidate, validateTags } from '../../lib/patch-fetch'\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport {\n  getDraftModeProviderForCacheScope,\n  workUnitAsyncStorage,\n} from '../../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../../response-cache'\nimport type { UnstableCacheStore } from '../../app-render/work-unit-async-storage.external'\n\ntype Callback = (...args: any[]) => Promise<any>\n\nlet noStoreFetchIdx = 0\n\nasync function cacheNewResult<T>(\n  result: T,\n  incrementalCache: IncrementalCache,\n  cacheKey: string,\n  tags: string[],\n  revalidate: number | false | undefined,\n  fetchIdx: number,\n  fetchUrl: string\n): Promise<unknown> {\n  await incrementalCache.set(\n    cacheKey,\n    {\n      kind: CachedRouteKind.FETCH,\n      data: {\n        headers: {},\n        // TODO: handle non-JSON values?\n        body: JSON.stringify(result),\n        status: 200,\n        url: '',\n      } satisfies CachedFetchData,\n      revalidate: typeof revalidate !== 'number' ? CACHE_ONE_YEAR : revalidate,\n    },\n    { fetchCache: true, tags, fetchIdx, fetchUrl }\n  )\n  return\n}\n\n/**\n * This function allows you to cache the results of expensive operations, like database queries, and reuse them across multiple requests.\n *\n * Read more: [Next.js Docs: `unstable_cache`](https://nextjs.org/docs/app/api-reference/functions/unstable_cache)\n */\nexport function unstable_cache<T extends Callback>(\n  cb: T,\n  keyParts?: string[],\n  options: {\n    /**\n     * The revalidation interval in seconds.\n     */\n    revalidate?: number | false\n    tags?: string[]\n  } = {}\n): T {\n  if (options.revalidate === 0) {\n    throw new Error(\n      `Invariant revalidate: 0 can not be passed to unstable_cache(), must be \"false\" or \"> 0\" ${cb.toString()}`\n    )\n  }\n\n  // Validate the tags provided are valid\n  const tags = options.tags\n    ? validateTags(options.tags, `unstable_cache ${cb.toString()}`)\n    : []\n\n  // Validate the revalidate options\n  validateRevalidate(\n    options.revalidate,\n    `unstable_cache ${cb.name || cb.toString()}`\n  )\n\n  // Stash the fixed part of the key at construction time. The invocation key will combine\n  // the fixed key with the arguments when actually called\n  // @TODO if cb.toString() is long we should hash it\n  // @TODO come up with a collision-free way to combine keyParts\n  // @TODO consider validating the keyParts are all strings. TS can't provide runtime guarantees\n  // and the error produced by accidentally using something that cannot be safely coerced is likely\n  // hard to debug\n  const fixedKey = `${cb.toString()}-${\n    Array.isArray(keyParts) && keyParts.join(',')\n  }`\n\n  const cachedCb = async (...args: any[]) => {\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // We must be able to find the incremental cache otherwise we throw\n    const maybeIncrementalCache:\n      | import('../../lib/incremental-cache').IncrementalCache\n      | undefined =\n      workStore?.incrementalCache || (globalThis as any).__incrementalCache\n\n    if (!maybeIncrementalCache) {\n      throw new Error(\n        `Invariant: incrementalCache missing in unstable_cache ${cb.toString()}`\n      )\n    }\n    const incrementalCache = maybeIncrementalCache\n\n    const cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n    try {\n      // If there's no request store, we aren't in a request (or we're not in app\n      // router)  and if there's no static generation store, we aren't in app\n      // router. Default to an empty pathname and search params when there's no\n      // request store or static generation store available.\n      const requestStore =\n        workUnitStore && workUnitStore.type === 'request'\n          ? workUnitStore\n          : undefined\n      const pathname = requestStore?.url.pathname ?? workStore?.route ?? ''\n      const searchParams = new URLSearchParams(requestStore?.url.search ?? '')\n\n      const sortedSearchKeys = [...searchParams.keys()].sort((a, b) => {\n        return a.localeCompare(b)\n      })\n      const sortedSearch = sortedSearchKeys\n        .map((key) => `${key}=${searchParams.get(key)}`)\n        .join('&')\n\n      // Construct the complete cache key for this function invocation\n      // @TODO stringify is likely not safe here. We will coerce undefined to null which will make\n      // the keyspace smaller than the execution space\n      const invocationKey = `${fixedKey}-${JSON.stringify(args)}`\n      const cacheKey = await incrementalCache.generateCacheKey(invocationKey)\n      // $urlWithPath,$sortedQueryStringKeys,$hashOfEveryThingElse\n      const fetchUrl = `unstable_cache ${pathname}${sortedSearch.length ? '?' : ''}${sortedSearch} ${cb.name ? ` ${cb.name}` : cacheKey}`\n      const fetchIdx =\n        (workStore ? workStore.nextFetchId : noStoreFetchIdx) ?? 1\n\n      const implicitTags = workUnitStore?.implicitTags\n\n      const innerCacheStore: UnstableCacheStore = {\n        type: 'unstable-cache',\n        phase: 'render',\n        implicitTags,\n        draftMode:\n          workUnitStore &&\n          workStore &&\n          getDraftModeProviderForCacheScope(workStore, workUnitStore),\n      }\n\n      if (workStore) {\n        workStore.nextFetchId = fetchIdx + 1\n\n        // We are in an App Router context. We try to return the cached entry if it exists and is valid\n        // If the entry is fresh we return it. If the entry is stale we return it but revalidate the entry in\n        // the background. If the entry is missing or invalid we generate a new entry and return it.\n\n        // We update the store's revalidate property if the option.revalidate is a higher precedence\n        if (\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n        ) {\n          // options.revalidate === undefined doesn't affect timing.\n          // options.revalidate === false doesn't shrink timing. it stays at the maximum.\n          if (typeof options.revalidate === 'number') {\n            if (workUnitStore.revalidate < options.revalidate) {\n              // The store is already revalidating on a shorter time interval, leave it alone\n            } else {\n              workUnitStore.revalidate = options.revalidate\n            }\n          }\n\n          // We need to accumulate the tags for this invocation within the store\n          const collectedTags = workUnitStore.tags\n          if (collectedTags === null) {\n            workUnitStore.tags = tags.slice()\n          } else {\n            for (const tag of tags) {\n              // @TODO refactor tags to be a set to avoid this O(n) lookup\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const isNestedUnstableCache =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n        if (\n          // when we are nested inside of other unstable_cache's\n          // we should bypass cache similar to fetches\n          !isNestedUnstableCache &&\n          workStore.fetchCache !== 'force-no-store' &&\n          !workStore.isOnDemandRevalidate &&\n          !incrementalCache.isOnDemandRevalidate &&\n          !workStore.isDraftMode\n        ) {\n          // We attempt to get the current cache entry from the incremental cache.\n          const cacheEntry = await incrementalCache.get(cacheKey, {\n            kind: IncrementalCacheKind.FETCH,\n            revalidate: options.revalidate,\n            tags,\n            softTags: implicitTags?.tags,\n            fetchIdx,\n            fetchUrl,\n          })\n\n          if (cacheEntry && cacheEntry.value) {\n            // The entry exists and has a value\n            if (cacheEntry.value.kind !== CachedRouteKind.FETCH) {\n              // The entry is invalid and we need a special warning\n              // @TODO why do we warn this way? Should this just be an error? How are these errors surfaced\n              // so bugs can be reported\n              // @TODO the invocation key can have sensitive data in it. we should not log this entire object\n              console.error(\n                `Invariant invalid cacheEntry returned for ${invocationKey}`\n              )\n              // will fall through to generating a new cache entry below\n            } else {\n              // We have a valid cache entry so we will be returning it. We also check to see if we need\n              // to background revalidate it by checking if it is stale.\n              const cachedResponse =\n                cacheEntry.value.data.body !== undefined\n                  ? JSON.parse(cacheEntry.value.data.body)\n                  : undefined\n              if (cacheEntry.isStale) {\n                // In App Router we return the stale result and revalidate in the background\n                if (!workStore.pendingRevalidates) {\n                  workStore.pendingRevalidates = {}\n                }\n\n                // We run the cache function asynchronously and save the result when it completes\n                workStore.pendingRevalidates[invocationKey] =\n                  workUnitAsyncStorage\n                    .run(innerCacheStore, cb, ...args)\n                    .then((result) => {\n                      return cacheNewResult(\n                        result,\n                        incrementalCache,\n                        cacheKey,\n                        tags,\n                        options.revalidate,\n                        fetchIdx,\n                        fetchUrl\n                      )\n                    })\n                    // @TODO This error handling seems wrong. We swallow the error?\n                    .catch((err) =>\n                      console.error(\n                        `revalidating cache with key: ${invocationKey}`,\n                        err\n                      )\n                    )\n              }\n              // We had a valid cache entry so we return it here\n              return cachedResponse\n            }\n          }\n        }\n\n        // If we got this far then we had an invalid cache entry and need to generate a new one\n        const result = await workUnitAsyncStorage.run(\n          innerCacheStore,\n          cb,\n          ...args\n        )\n\n        if (!workStore.isDraftMode) {\n          if (!workStore.pendingRevalidates) {\n            workStore.pendingRevalidates = {}\n          }\n\n          // We need to push the cache result promise to pending\n          // revalidates otherwise it won't be awaited and is just\n          // dangling\n          workStore.pendingRevalidates[invocationKey] = cacheNewResult(\n            result,\n            incrementalCache,\n            cacheKey,\n            tags,\n            options.revalidate,\n            fetchIdx,\n            fetchUrl\n          )\n        }\n\n        return result\n      } else {\n        noStoreFetchIdx += 1\n        // We are in Pages Router or were called outside of a render. We don't have a store\n        // so we just call the callback directly when it needs to run.\n        // If the entry is fresh we return it. If the entry is stale we return it but revalidate the entry in\n        // the background. If the entry is missing or invalid we generate a new entry and return it.\n\n        if (!incrementalCache.isOnDemandRevalidate) {\n          // We aren't doing an on demand revalidation so we check use the cache if valid\n          const cacheEntry = await incrementalCache.get(cacheKey, {\n            kind: IncrementalCacheKind.FETCH,\n            revalidate: options.revalidate,\n            tags,\n            fetchIdx,\n            fetchUrl,\n            softTags: implicitTags?.tags,\n          })\n\n          if (cacheEntry && cacheEntry.value) {\n            // The entry exists and has a value\n            if (cacheEntry.value.kind !== CachedRouteKind.FETCH) {\n              // The entry is invalid and we need a special warning\n              // @TODO why do we warn this way? Should this just be an error? How are these errors surfaced\n              // so bugs can be reported\n              console.error(\n                `Invariant invalid cacheEntry returned for ${invocationKey}`\n              )\n              // will fall through to generating a new cache entry below\n            } else if (!cacheEntry.isStale) {\n              // We have a valid cache entry and it is fresh so we return it\n              return cacheEntry.value.data.body !== undefined\n                ? JSON.parse(cacheEntry.value.data.body)\n                : undefined\n            }\n          }\n        }\n\n        // If we got this far then we had an invalid cache entry and need to generate a new one\n        const result = await workUnitAsyncStorage.run(\n          innerCacheStore,\n          cb,\n          ...args\n        )\n\n        // we need to wait setting the new cache result here as\n        // we don't have pending revalidates on workStore to\n        // push to and we can't have a dangling promise\n        await cacheNewResult(\n          result,\n          incrementalCache,\n          cacheKey,\n          tags,\n          options.revalidate,\n          fetchIdx,\n          fetchUrl\n        )\n        return result\n      }\n    } finally {\n      if (cacheSignal) {\n        cacheSignal.endRead()\n      }\n    }\n  }\n  // TODO: once AsyncLocalStorage.run() returns the correct types this override will no longer be necessary\n  return cachedCb as unknown as T\n}\n"], "names": ["CACHE_ONE_YEAR", "validateRevalidate", "validateTags", "workAsyncStorage", "getDraftModeProviderForCacheScope", "workUnitAsyncStorage", "CachedRouteKind", "IncrementalCacheKind", "noStoreFetchIdx", "cacheNewResult", "result", "incrementalCache", "cache<PERSON>ey", "tags", "revalidate", "fetchIdx", "fetchUrl", "set", "kind", "FETCH", "data", "headers", "body", "JSON", "stringify", "status", "url", "fetchCache", "unstable_cache", "cb", "keyParts", "options", "Error", "toString", "name", "fixedKey", "Array", "isArray", "join", "cachedCb", "args", "workStore", "getStore", "workUnitStore", "maybeIncrementalCache", "globalThis", "__incrementalCache", "cacheSignal", "type", "beginRead", "requestStore", "undefined", "pathname", "route", "searchParams", "URLSearchParams", "search", "sortedSearchKeys", "keys", "sort", "a", "b", "localeCompare", "sortedSearch", "map", "key", "get", "invocation<PERSON><PERSON>", "generate<PERSON>ache<PERSON>ey", "length", "nextFetchId", "implicitTags", "innerCacheStore", "phase", "draftMode", "collectedTags", "slice", "tag", "includes", "push", "isNestedUnstableCache", "isOnDemandRevalidate", "isDraftMode", "cacheEntry", "softTags", "value", "console", "error", "cachedResponse", "parse", "isStale", "pendingRevalidates", "run", "then", "catch", "err", "endRead"], "mappings": "AAEA,SAASA,cAAc,QAAQ,yBAAwB;AACvD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,wBAAuB;AACxE,SAASC,gBAAgB,QAAQ,+CAA8C;AAC/E,SACEC,iCAAiC,EACjCC,oBAAoB,QACf,oDAAmD;AAC1D,SACEC,eAAe,EACfC,oBAAoB,QAEf,uBAAsB;AAK7B,IAAIC,kBAAkB;AAEtB,eAAeC,eACbC,MAAS,EACTC,gBAAkC,EAClCC,QAAgB,EAChBC,IAAc,EACdC,UAAsC,EACtCC,QAAgB,EAChBC,QAAgB;IAEhB,MAAML,iBAAiBM,GAAG,CACxBL,UACA;QACEM,MAAMZ,gBAAgBa,KAAK;QAC3BC,MAAM;YACJC,SAAS,CAAC;YACV,gCAAgC;YAChCC,MAAMC,KAAKC,SAAS,CAACd;YACrBe,QAAQ;YACRC,KAAK;QACP;QACAZ,YAAY,OAAOA,eAAe,WAAWd,iBAAiBc;IAChE,GACA;QAAEa,YAAY;QAAMd;QAAME;QAAUC;IAAS;IAE/C;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASY,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAMI,CAAC,CAAC;IAEN,IAAIA,QAAQjB,UAAU,KAAK,GAAG;QAC5B,MAAM,qBAEL,CAFK,IAAIkB,MACR,CAAC,wFAAwF,EAAEH,GAAGI,QAAQ,IAAI,GADtG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,uCAAuC;IACvC,MAAMpB,OAAOkB,QAAQlB,IAAI,GACrBX,aAAa6B,QAAQlB,IAAI,EAAE,CAAC,eAAe,EAAEgB,GAAGI,QAAQ,IAAI,IAC5D,EAAE;IAEN,kCAAkC;IAClChC,mBACE8B,QAAQjB,UAAU,EAClB,CAAC,eAAe,EAAEe,GAAGK,IAAI,IAAIL,GAAGI,QAAQ,IAAI;IAG9C,wFAAwF;IACxF,wDAAwD;IACxD,mDAAmD;IACnD,8DAA8D;IAC9D,8FAA8F;IAC9F,iGAAiG;IACjG,gBAAgB;IAChB,MAAME,WAAW,GAAGN,GAAGI,QAAQ,GAAG,CAAC,EACjCG,MAAMC,OAAO,CAACP,aAAaA,SAASQ,IAAI,CAAC,MACzC;IAEF,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,YAAYtC,iBAAiBuC,QAAQ;QAC3C,MAAMC,gBAAgBtC,qBAAqBqC,QAAQ;QAEnD,mEAAmE;QACnE,MAAME,wBAGJH,CAAAA,6BAAAA,UAAW9B,gBAAgB,KAAI,AAACkC,WAAmBC,kBAAkB;QAEvE,IAAI,CAACF,uBAAuB;YAC1B,MAAM,qBAEL,CAFK,IAAIZ,MACR,CAAC,sDAAsD,EAAEH,GAAGI,QAAQ,IAAI,GADpE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMtB,mBAAmBiC;QAEzB,MAAMG,cACJJ,iBAAiBA,cAAcK,IAAI,KAAK,cACpCL,cAAcI,WAAW,GACzB;QACN,IAAIA,aAAa;YACfA,YAAYE,SAAS;QACvB;QACA,IAAI;YACF,2EAA2E;YAC3E,uEAAuE;YACvE,yEAAyE;YACzE,sDAAsD;YACtD,MAAMC,eACJP,iBAAiBA,cAAcK,IAAI,KAAK,YACpCL,gBACAQ;YACN,MAAMC,WAAWF,CAAAA,gCAAAA,aAAcxB,GAAG,CAAC0B,QAAQ,MAAIX,6BAAAA,UAAWY,KAAK,KAAI;YACnE,MAAMC,eAAe,IAAIC,gBAAgBL,CAAAA,gCAAAA,aAAcxB,GAAG,CAAC8B,MAAM,KAAI;YAErE,MAAMC,mBAAmB;mBAAIH,aAAaI,IAAI;aAAG,CAACC,IAAI,CAAC,CAACC,GAAGC;gBACzD,OAAOD,EAAEE,aAAa,CAACD;YACzB;YACA,MAAME,eAAeN,iBAClBO,GAAG,CAAC,CAACC,MAAQ,GAAGA,IAAI,CAAC,EAAEX,aAAaY,GAAG,CAACD,MAAM,EAC9C3B,IAAI,CAAC;YAER,gEAAgE;YAChE,4FAA4F;YAC5F,gDAAgD;YAChD,MAAM6B,gBAAgB,GAAGhC,SAAS,CAAC,EAAEZ,KAAKC,SAAS,CAACgB,OAAO;YAC3D,MAAM5B,WAAW,MAAMD,iBAAiByD,gBAAgB,CAACD;YACzD,4DAA4D;YAC5D,MAAMnD,WAAW,CAAC,eAAe,EAAEoC,WAAWW,aAAaM,MAAM,GAAG,MAAM,KAAKN,aAAa,CAAC,EAAElC,GAAGK,IAAI,GAAG,CAAC,CAAC,EAAEL,GAAGK,IAAI,EAAE,GAAGtB,UAAU;YACnI,MAAMG,WACJ,AAAC0B,CAAAA,YAAYA,UAAU6B,WAAW,GAAG9D,eAAc,KAAM;YAE3D,MAAM+D,eAAe5B,iCAAAA,cAAe4B,YAAY;YAEhD,MAAMC,kBAAsC;gBAC1CxB,MAAM;gBACNyB,OAAO;gBACPF;gBACAG,WACE/B,iBACAF,aACArC,kCAAkCqC,WAAWE;YACjD;YAEA,IAAIF,WAAW;gBACbA,UAAU6B,WAAW,GAAGvD,WAAW;gBAEnC,+FAA+F;gBAC/F,qGAAqG;gBACrG,4FAA4F;gBAE5F,4FAA4F;gBAC5F,IACE4B,iBACCA,CAAAA,cAAcK,IAAI,KAAK,WACtBL,cAAcK,IAAI,KAAK,eACvBL,cAAcK,IAAI,KAAK,mBACvBL,cAAcK,IAAI,KAAK,kBAAiB,GAC1C;oBACA,0DAA0D;oBAC1D,+EAA+E;oBAC/E,IAAI,OAAOjB,QAAQjB,UAAU,KAAK,UAAU;wBAC1C,IAAI6B,cAAc7B,UAAU,GAAGiB,QAAQjB,UAAU,EAAE;wBACjD,+EAA+E;wBACjF,OAAO;4BACL6B,cAAc7B,UAAU,GAAGiB,QAAQjB,UAAU;wBAC/C;oBACF;oBAEA,sEAAsE;oBACtE,MAAM6D,gBAAgBhC,cAAc9B,IAAI;oBACxC,IAAI8D,kBAAkB,MAAM;wBAC1BhC,cAAc9B,IAAI,GAAGA,KAAK+D,KAAK;oBACjC,OAAO;wBACL,KAAK,MAAMC,OAAOhE,KAAM;4BACtB,4DAA4D;4BAC5D,IAAI,CAAC8D,cAAcG,QAAQ,CAACD,MAAM;gCAChCF,cAAcI,IAAI,CAACF;4BACrB;wBACF;oBACF;gBACF;gBAEA,MAAMG,wBACJrC,iBAAiBA,cAAcK,IAAI,KAAK;gBAC1C,IACE,sDAAsD;gBACtD,4CAA4C;gBAC5C,CAACgC,yBACDvC,UAAUd,UAAU,KAAK,oBACzB,CAACc,UAAUwC,oBAAoB,IAC/B,CAACtE,iBAAiBsE,oBAAoB,IACtC,CAACxC,UAAUyC,WAAW,EACtB;oBACA,wEAAwE;oBACxE,MAAMC,aAAa,MAAMxE,iBAAiBuD,GAAG,CAACtD,UAAU;wBACtDM,MAAMX,qBAAqBY,KAAK;wBAChCL,YAAYiB,QAAQjB,UAAU;wBAC9BD;wBACAuE,QAAQ,EAAEb,gCAAAA,aAAc1D,IAAI;wBAC5BE;wBACAC;oBACF;oBAEA,IAAImE,cAAcA,WAAWE,KAAK,EAAE;wBAClC,mCAAmC;wBACnC,IAAIF,WAAWE,KAAK,CAACnE,IAAI,KAAKZ,gBAAgBa,KAAK,EAAE;4BACnD,qDAAqD;4BACrD,6FAA6F;4BAC7F,0BAA0B;4BAC1B,+FAA+F;4BAC/FmE,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEpB,eAAe;wBAE9D,0DAA0D;wBAC5D,OAAO;4BACL,0FAA0F;4BAC1F,0DAA0D;4BAC1D,MAAMqB,iBACJL,WAAWE,KAAK,CAACjE,IAAI,CAACE,IAAI,KAAK6B,YAC3B5B,KAAKkE,KAAK,CAACN,WAAWE,KAAK,CAACjE,IAAI,CAACE,IAAI,IACrC6B;4BACN,IAAIgC,WAAWO,OAAO,EAAE;gCACtB,4EAA4E;gCAC5E,IAAI,CAACjD,UAAUkD,kBAAkB,EAAE;oCACjClD,UAAUkD,kBAAkB,GAAG,CAAC;gCAClC;gCAEA,iFAAiF;gCACjFlD,UAAUkD,kBAAkB,CAACxB,cAAc,GACzC9D,qBACGuF,GAAG,CAACpB,iBAAiB3C,OAAOW,MAC5BqD,IAAI,CAAC,CAACnF;oCACL,OAAOD,eACLC,QACAC,kBACAC,UACAC,MACAkB,QAAQjB,UAAU,EAClBC,UACAC;gCAEJ,EACA,+DAA+D;iCAC9D8E,KAAK,CAAC,CAACC,MACNT,QAAQC,KAAK,CACX,CAAC,6BAA6B,EAAEpB,eAAe,EAC/C4B;4BAGV;4BACA,kDAAkD;4BAClD,OAAOP;wBACT;oBACF;gBACF;gBAEA,uFAAuF;gBACvF,MAAM9E,SAAS,MAAML,qBAAqBuF,GAAG,CAC3CpB,iBACA3C,OACGW;gBAGL,IAAI,CAACC,UAAUyC,WAAW,EAAE;oBAC1B,IAAI,CAACzC,UAAUkD,kBAAkB,EAAE;wBACjClD,UAAUkD,kBAAkB,GAAG,CAAC;oBAClC;oBAEA,sDAAsD;oBACtD,wDAAwD;oBACxD,WAAW;oBACXlD,UAAUkD,kBAAkB,CAACxB,cAAc,GAAG1D,eAC5CC,QACAC,kBACAC,UACAC,MACAkB,QAAQjB,UAAU,EAClBC,UACAC;gBAEJ;gBAEA,OAAON;YACT,OAAO;gBACLF,mBAAmB;gBACnB,mFAAmF;gBACnF,8DAA8D;gBAC9D,qGAAqG;gBACrG,4FAA4F;gBAE5F,IAAI,CAACG,iBAAiBsE,oBAAoB,EAAE;oBAC1C,+EAA+E;oBAC/E,MAAME,aAAa,MAAMxE,iBAAiBuD,GAAG,CAACtD,UAAU;wBACtDM,MAAMX,qBAAqBY,KAAK;wBAChCL,YAAYiB,QAAQjB,UAAU;wBAC9BD;wBACAE;wBACAC;wBACAoE,QAAQ,EAAEb,gCAAAA,aAAc1D,IAAI;oBAC9B;oBAEA,IAAIsE,cAAcA,WAAWE,KAAK,EAAE;wBAClC,mCAAmC;wBACnC,IAAIF,WAAWE,KAAK,CAACnE,IAAI,KAAKZ,gBAAgBa,KAAK,EAAE;4BACnD,qDAAqD;4BACrD,6FAA6F;4BAC7F,0BAA0B;4BAC1BmE,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEpB,eAAe;wBAE9D,0DAA0D;wBAC5D,OAAO,IAAI,CAACgB,WAAWO,OAAO,EAAE;4BAC9B,8DAA8D;4BAC9D,OAAOP,WAAWE,KAAK,CAACjE,IAAI,CAACE,IAAI,KAAK6B,YAClC5B,KAAKkE,KAAK,CAACN,WAAWE,KAAK,CAACjE,IAAI,CAACE,IAAI,IACrC6B;wBACN;oBACF;gBACF;gBAEA,uFAAuF;gBACvF,MAAMzC,SAAS,MAAML,qBAAqBuF,GAAG,CAC3CpB,iBACA3C,OACGW;gBAGL,uDAAuD;gBACvD,oDAAoD;gBACpD,+CAA+C;gBAC/C,MAAM/B,eACJC,QACAC,kBACAC,UACAC,MACAkB,QAAQjB,UAAU,EAClBC,UACAC;gBAEF,OAAON;YACT;QACF,SAAU;YACR,IAAIqC,aAAa;gBACfA,YAAYiD,OAAO;YACrB;QACF;IACF;IACA,yGAAyG;IACzG,OAAOzD;AACT", "ignoreList": [0]}