{"version": 3, "sources": ["../../../../src/server/typescript/rules/config.ts"], "sourcesContent": ["// This module provides intellisense for page and layout's exported configs.\n\nimport {\n  getSource,\n  isPositionInsideNode,\n  getTs,\n  removeStringQuotes,\n} from '../utils'\nimport {\n  NEXT_TS_ERRORS,\n  ALLOWED_EXPORTS,\n  LEGACY_CONFIG_EXPORT,\n} from '../constant'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst API_DOCS: Record<\n  string,\n  {\n    description: string\n    options?: Record<string, string>\n    link?: string\n    type?: string\n    isValid?: (value: string) => boolean\n    getHint?: (value: any) => string | undefined\n    insertText?: string\n  }\n> = {\n  dynamic: {\n    description:\n      'The `dynamic` option provides a few ways to opt in or out of dynamic behavior.',\n    options: {\n      '\"auto\"':\n        \"Heuristic to cache as much as possible but doesn't prevent any component to opt-in to dynamic behavior.\",\n      '\"force-dynamic\"':\n        'This disables all caching of fetches and always revalidates. (This is equivalent to `getServerSideProps`.)',\n      '\"error\"':\n        'This errors if any dynamic Hooks or fetches are used. (This is equivalent to `getStaticProps`.)',\n      '\"force-static\"':\n        'This forces caching of all fetches and returns empty values from `cookies`, `headers` and `useSearchParams`.',\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#dynamic',\n  },\n  fetchCache: {\n    description:\n      \"The `fetchCache` option controls how Next.js statically caches fetches. By default it statically caches fetches reachable before any dynamic Hooks are used, and it doesn't cache fetches that are discovered after that.\",\n    options: {\n      '\"force-no-store\"':\n        \"This lets you intentionally opt-out of all caching of data. This option forces all fetches to be refetched every request even if the `cache: 'force-cache'` option is passed to `fetch()`.\",\n      '\"only-no-store\"':\n        \"This lets you enforce that all data opts out of caching. This option makes `fetch()` reject with an error if `cache: 'force-cache'` is provided. It also changes the default to `no-store`.\",\n      '\"default-no-store\"':\n        \"Allows any explicit `cache` option to be passed to `fetch()` but if `'default'`, or no option, is provided then it defaults to `'no-store'`. This means that even fetches before a dynamic Hook are considered dynamic.\",\n      '\"auto\"':\n        \"This is the default option. It caches any fetches with the default `cache` option provided, that happened before a dynamic Hook is used and don't cache any such fetches if they're issued after a dynamic Hook.\",\n      '\"default-cache\"':\n        \"Allows any explicit `cache` option to be passed to `fetch()` but if `'default'`, or no option, is provided then it defaults to `'force-cache'`. This means that even fetches before a dynamic Hook are considered dynamic.\",\n      '\"only-cache\"':\n        \"This lets you enforce that all data opts into caching. This option makes `fetch()` reject with an error if `cache: 'force-cache'` is provided. It also changes the default to `force-cache`. This error can be discovered early during static builds - or dynamically during Edge rendering.\",\n      '\"force-cache\"':\n        \"This lets you intentionally opt-in to all caching of data. This option forces all fetches to be cache even if the `cache: 'no-store'` option is passed to `fetch()`.\",\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#fetchcache',\n  },\n  preferredRegion: {\n    description:\n      'Specify the perferred region that this layout or page should be deployed to. If the region option is not specified, it inherits the option from the nearest parent layout. The root defaults to `\"auto\"`.\\n\\nYou can also specify a region, such as \"iad1\", or an array of regions, such as `[\"iad1\", \"sfo1\"]`.',\n    options: {\n      '\"auto\"':\n        'Next.js will first deploy to the `\"home\"` region. Then if it doesn\\'t detect any waterfall requests after a few requests, it can upgrade that route, to be deployed globally. If it detects any waterfall requests after that, it can eventually downgrade back to `\"home`\".',\n      '\"global\"': 'Prefer deploying globally.',\n      '\"home\"': 'Prefer deploying to the Home region.',\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#preferredregion',\n    isValid: (value: string) => {\n      try {\n        const parsed = JSON.parse(value)\n        return (\n          typeof parsed === 'string' ||\n          (Array.isArray(parsed) && !parsed.some((v) => typeof v !== 'string'))\n        )\n      } catch (err) {\n        return false\n      }\n    },\n    getHint: (value: any) => {\n      if (value === 'auto') return `Automatically chosen by Next.js.`\n      if (value === 'global') return `Prefer deploying globally.`\n      if (value === 'home') return `Prefer deploying to the Home region.`\n      if (Array.isArray(value)) return `Deploy to regions: ${value.join(', ')}.`\n      if (typeof value === 'string') return `Deploy to region: ${value}.`\n    },\n  },\n  revalidate: {\n    description:\n      \"The `revalidate` option sets the default revalidation time for that layout or page. Note that it doesn't override the value specify by each `fetch()`.\",\n    type: 'mixed',\n    options: {\n      false:\n        'This is the default and changes the fetch cache to indefinitely cache anything that uses force-cache or is fetched before a dynamic Hook/fetch.',\n      0: 'Specifying `0` implies that this layout or page should never be static.',\n      30: 'Set the revalidation time to `30` seconds. The value can be `0` or any positive number.',\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#revalidate',\n    isValid: (value: string) => {\n      return value === 'false' || Number(value.replace(/_/g, '')) >= 0\n    },\n    getHint: (value: any) => {\n      return `Set the default revalidation time to \\`${value}\\` seconds.`\n    },\n  },\n  dynamicParams: {\n    description:\n      '`dynamicParams` replaces the `fallback` option of `getStaticPaths`. It controls whether we allow `dynamicParams` beyond the generated static params from `generateStaticParams`.',\n    options: {\n      true: 'Allow rendering dynamic params that are not generated by `generateStaticParams`.',\n      false:\n        'Disallow rendering dynamic params that are not generated by `generateStaticParams`.',\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#dynamicparams',\n    isValid: (value: string) => {\n      return value === 'true' || value === 'false'\n    },\n  },\n  runtime: {\n    description:\n      'The `runtime` option controls the preferred runtime to render this route.',\n    options: {\n      '\"nodejs\"': 'Prefer the Node.js runtime.',\n      '\"edge\"': 'Prefer the Edge runtime.',\n      '\"experimental-edge\"': `@deprecated\\n\\nThis option is no longer experimental. Use \\`edge\\` instead.`,\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#runtime',\n  },\n  metadata: {\n    description: 'Next.js Metadata configurations',\n    link: 'https://nextjs.org/docs/app/building-your-application/optimizing/metadata',\n    insertText: 'metadata: Metadata = {};',\n  },\n  generateMetadata: {\n    description: 'Next.js generateMetadata configurations',\n    link: 'https://nextjs.org/docs/app/api-reference/functions/generate-metadata',\n    insertText: 'generateMetadata = (): Metadata => { return {} };',\n  },\n  maxDuration: {\n    description:\n      '`maxDuration` allows you to set max default execution time for your function. If it is not specified, the default value is dependent on your deployment platform and plan.',\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#maxduration',\n  },\n  experimental_ppr: {\n    description: `Enables experimental Partial Prerendering (PPR) for this page / layout, when PPR is set to \"incremental\" mode.`,\n    link: 'https://nextjs.org/docs/app/api-reference/next-config-js/ppr',\n    options: {\n      true: 'Enable PPR for this route',\n      false: 'Disable PPR for this route',\n    },\n    isValid: (value: string) => {\n      return value === 'true' || value === 'false'\n    },\n  },\n}\n\nfunction visitEntryConfig(\n  fileName: string,\n  position: number,\n  callback: (entryEonfig: string, value: tsModule.VariableDeclaration) => void\n) {\n  const source = getSource(fileName)\n  if (source) {\n    const ts = getTs()\n    ts.forEachChild(source, function visit(node) {\n      // Covered by this node\n      if (isPositionInsideNode(position, node)) {\n        // Export variable\n        if (\n          ts.isVariableStatement(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          if (ts.isVariableDeclarationList(node.declarationList)) {\n            for (const declaration of node.declarationList.declarations) {\n              if (isPositionInsideNode(position, declaration)) {\n                // `export const ... = ...`\n                const text = declaration.name.getText()\n                callback(text, declaration)\n              }\n            }\n          }\n        }\n      }\n    })\n  }\n}\n\nfunction createAutoCompletionOptionName(sort: number, name: string) {\n  const ts = getTs()\n\n  return {\n    name,\n    insertText: API_DOCS[name].insertText,\n    sortText: '!' + sort,\n    kind: ts.ScriptElementKind.constElement,\n    kindModifiers: ts.ScriptElementKindModifier.exportedModifier,\n    labelDetails: {\n      description: `Next.js ${name} option`,\n    },\n    data: {\n      exportName: name,\n      moduleSpecifier: 'next/typescript/entry_option_name',\n    },\n  } as tsModule.CompletionEntry\n}\n\nfunction createAutoCompletionOptionValue(\n  sort: number,\n  name: string,\n  apiName: string\n) {\n  const ts = getTs()\n  const isString = name.startsWith('\"')\n  return {\n    name,\n    insertText: removeStringQuotes(name),\n    sortText: '' + sort,\n    kind: isString ? ts.ScriptElementKind.string : ts.ScriptElementKind.unknown,\n    kindModifiers: ts.ScriptElementKindModifier.none,\n    labelDetails: {\n      description: `Next.js ${apiName} option`,\n    },\n    data: {\n      exportName: apiName,\n      moduleSpecifier: 'next/typescript/entry_option_value',\n    },\n  } as tsModule.CompletionEntry\n}\n\nfunction getAPIDescription(api: string): string {\n  return (\n    API_DOCS[api].description +\n    '\\n\\n' +\n    Object.entries(API_DOCS[api].options || {})\n      .map(([key, value]) => `- \\`${key}\\`: ${value}`)\n      .join('\\n')\n  )\n}\n\nconst config = {\n  // Auto completion for entry exported configs.\n  addCompletionsAtPosition(\n    fileName: string,\n    position: number,\n    prior: tsModule.WithMetadata<tsModule.CompletionInfo>\n  ) {\n    visitEntryConfig(fileName, position, (entryConfig, declaration) => {\n      if (!API_DOCS[entryConfig]) {\n        if (isPositionInsideNode(position, declaration.name)) {\n          prior.entries.push(\n            ...Object.keys(API_DOCS).map((name, index) => {\n              return createAutoCompletionOptionName(index, name)\n            })\n          )\n        }\n        return\n      }\n\n      prior.entries.push(\n        ...Object.keys(API_DOCS[entryConfig].options || {}).map(\n          (name, index) => {\n            return createAutoCompletionOptionValue(index, name, entryConfig)\n          }\n        )\n      )\n    })\n  },\n\n  // Show docs when hovering on the exported configs.\n  getQuickInfoAtPosition(fileName: string, position: number) {\n    const ts = getTs()\n\n    let overridden: tsModule.QuickInfo | undefined\n    visitEntryConfig(fileName, position, (entryConfig, declaration) => {\n      if (!API_DOCS[entryConfig]) return\n\n      const name = declaration.name\n      const value = declaration.initializer\n\n      const docsLink = {\n        kind: 'text',\n        text:\n          `\\n\\nRead more about the \"${entryConfig}\" option: ` +\n          API_DOCS[entryConfig].link,\n      }\n\n      if (value && isPositionInsideNode(position, value)) {\n        // Hovers the value of the config\n        const isString = ts.isStringLiteral(value)\n        const text = removeStringQuotes(value.getText())\n        const key = isString ? `\"${text}\"` : text\n\n        const isValid = API_DOCS[entryConfig].isValid\n          ? API_DOCS[entryConfig].isValid?.(key)\n          : !!API_DOCS[entryConfig].options?.[key]\n\n        if (isValid) {\n          overridden = {\n            kind: ts.ScriptElementKind.enumElement,\n            kindModifiers: ts.ScriptElementKindModifier.none,\n            textSpan: {\n              start: value.getStart(),\n              length: value.getWidth(),\n            },\n            displayParts: [],\n            documentation: [\n              {\n                kind: 'text',\n                text:\n                  API_DOCS[entryConfig].options?.[key] ||\n                  API_DOCS[entryConfig].getHint?.(key) ||\n                  '',\n              },\n              docsLink,\n            ],\n          }\n        } else {\n          // Wrong value, display the docs link\n          overridden = {\n            kind: ts.ScriptElementKind.enumElement,\n            kindModifiers: ts.ScriptElementKindModifier.none,\n            textSpan: {\n              start: value.getStart(),\n              length: value.getWidth(),\n            },\n            displayParts: [],\n            documentation: [docsLink],\n          }\n        }\n      } else {\n        // Hovers the name of the config\n        overridden = {\n          kind: ts.ScriptElementKind.enumElement,\n          kindModifiers: ts.ScriptElementKindModifier.none,\n          textSpan: {\n            start: name.getStart(),\n            length: name.getWidth(),\n          },\n          displayParts: [],\n          documentation: [\n            {\n              kind: 'text',\n              text: getAPIDescription(entryConfig),\n            },\n            docsLink,\n          ],\n        }\n      }\n    })\n    return overridden\n  },\n\n  // Show details on the side when auto completing.\n  getCompletionEntryDetails(\n    entryName: string,\n    data: tsModule.CompletionEntryData,\n    fileName: string\n  ): tsModule.CompletionEntryDetails | undefined {\n    const ts = getTs()\n    if (\n      data &&\n      data.moduleSpecifier &&\n      data.moduleSpecifier.startsWith('next/typescript')\n    ) {\n      let content = ''\n      if (data.moduleSpecifier === 'next/typescript/entry_option_name') {\n        content = getAPIDescription(entryName)\n      } else {\n        const options = API_DOCS[data.exportName].options\n        if (!options) return\n        content = options[entryName]\n      }\n\n      if (entryName === 'metadata' || entryName === 'generateMetadata') {\n        const sourceFile = getSource(fileName)\n        let start = 0\n        let foundMetadataImport = false\n\n        if (sourceFile) {\n          const visitor: tsModule.Visitor = (node) => {\n            // Check for top directive\n            if (\n              ts.isExpressionStatement(node) &&\n              ts.isStringLiteral(node.expression) &&\n              node.expression.getStart() === 0\n            ) {\n              const text = node.expression.text\n              if (text.startsWith('use ')) {\n                start = node.end + 1\n                return node // Continue traversal\n              }\n            }\n\n            // Check for Metadata import\n            if (\n              ts.isImportDeclaration(node) &&\n              (node.moduleSpecifier.getText() === '\"next\"' ||\n                node.moduleSpecifier.getText() === \"'next'\")\n            ) {\n              const namedImports = node.importClause?.namedBindings\n              if (namedImports && ts.isNamedImports(namedImports)) {\n                foundMetadataImport = namedImports.elements.some((element) => {\n                  const name = element.name.getText()\n                  const propertyName = element.propertyName?.getText()\n                  return name === 'Metadata' || propertyName === 'Metadata'\n                })\n                if (foundMetadataImport) {\n                  return // Stop traversal\n                }\n              }\n            }\n\n            return node\n          }\n\n          for (const statement of sourceFile.statements) {\n            if (foundMetadataImport) break\n            ts.visitNode(statement, visitor)\n          }\n        }\n\n        return {\n          name: entryName,\n          kind: ts.ScriptElementKind.enumElement,\n          kindModifiers: ts.ScriptElementKindModifier.none,\n          displayParts: [],\n          codeActions: foundMetadataImport\n            ? undefined\n            : [\n                {\n                  description: `Import type 'Metadata' from module 'next'`,\n                  changes: [\n                    {\n                      fileName,\n                      textChanges: [\n                        {\n                          span: { start, length: 0 },\n                          newText: `import type { Metadata } from 'next';\\n`,\n                        },\n                      ],\n                    },\n                  ],\n                },\n              ],\n          documentation: [\n            {\n              kind: 'text',\n              text: content,\n            },\n          ],\n        }\n      }\n\n      return {\n        name: entryName,\n        kind: ts.ScriptElementKind.enumElement,\n        kindModifiers: ts.ScriptElementKindModifier.none,\n        displayParts: [],\n        documentation: [\n          {\n            kind: 'text',\n            text: content,\n          },\n        ],\n      }\n    }\n  },\n\n  // Show errors for invalid export fields.\n  getSemanticDiagnosticsForExportVariableStatement(\n    source: tsModule.SourceFile,\n    node: tsModule.VariableStatement\n  ) {\n    const ts = getTs()\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    // Check if it has correct option exports\n    if (ts.isVariableDeclarationList(node.declarationList)) {\n      for (const declaration of node.declarationList.declarations) {\n        const name = declaration.name\n        if (ts.isIdentifier(name)) {\n          if (!ALLOWED_EXPORTS.includes(name.text) && !API_DOCS[name.text]) {\n            diagnostics.push({\n              file: source,\n              category: ts.DiagnosticCategory.Error,\n              code: NEXT_TS_ERRORS.INVALID_ENTRY_EXPORT,\n              messageText: `\"${name.text}\" is not a valid Next.js entry export value.`,\n              start: name.getStart(),\n              length: name.getWidth(),\n            })\n          } else if (API_DOCS[name.text]) {\n            // Check if the value is valid\n            const value = declaration.initializer\n            const options = API_DOCS[name.text].options\n\n            if (value && options) {\n              let displayedValue = ''\n              let errorMessage = ''\n              let isInvalid = false\n\n              if (\n                ts.isStringLiteral(value) ||\n                ts.isNoSubstitutionTemplateLiteral(value)\n              ) {\n                const val = '\"' + removeStringQuotes(value.getText()) + '\"'\n                const allowedValues = Object.keys(options).filter((v) =>\n                  /^['\"]/.test(v)\n                )\n\n                if (\n                  !allowedValues.includes(val) &&\n                  !API_DOCS[name.text].isValid?.(val)\n                ) {\n                  isInvalid = true\n                  displayedValue = val\n                }\n              } else if (\n                ts.isNumericLiteral(value) ||\n                (ts.isPrefixUnaryExpression(value) &&\n                  ts.isMinusToken((value as any).operator) &&\n                  (ts.isNumericLiteral((value as any).operand.kind) ||\n                    (ts.isIdentifier((value as any).operand.kind) &&\n                      (value as any).operand.kind.getText() === 'Infinity'))) ||\n                (ts.isIdentifier(value) && value.getText() === 'Infinity')\n              ) {\n                const v = value.getText()\n                if (!API_DOCS[name.text].isValid?.(v)) {\n                  isInvalid = true\n                  displayedValue = v\n                }\n              } else if (\n                value.kind === ts.SyntaxKind.TrueKeyword ||\n                value.kind === ts.SyntaxKind.FalseKeyword\n              ) {\n                const v = value.getText()\n                if (!API_DOCS[name.text].isValid?.(v)) {\n                  isInvalid = true\n                  displayedValue = v\n                }\n              } else if (ts.isArrayLiteralExpression(value)) {\n                const v = value.getText()\n                if (\n                  !API_DOCS[name.text].isValid?.(\n                    JSON.stringify(value.elements.map((e) => e.getText()))\n                  )\n                ) {\n                  isInvalid = true\n                  displayedValue = v\n                }\n              } else if (\n                // Other literals\n                ts.isBigIntLiteral(value) ||\n                ts.isObjectLiteralExpression(value) ||\n                ts.isRegularExpressionLiteral(value) ||\n                ts.isPrefixUnaryExpression(value)\n              ) {\n                isInvalid = true\n                displayedValue = value.getText()\n              } else {\n                // Not a literal, error because it's not statically analyzable\n                isInvalid = true\n                displayedValue = value.getText()\n                errorMessage = `\"${displayedValue}\" is not a valid value for the \"${name.text}\" option. The configuration must be statically analyzable.`\n              }\n\n              if (isInvalid) {\n                diagnostics.push({\n                  file: source,\n                  category: ts.DiagnosticCategory.Error,\n                  code: NEXT_TS_ERRORS.INVALID_OPTION_VALUE,\n                  messageText:\n                    errorMessage ||\n                    `\"${displayedValue}\" is not a valid value for the \"${name.text}\" option.`,\n                  start: value.getStart(),\n                  length: value.getWidth(),\n                })\n              }\n            }\n          } else if (name.text === LEGACY_CONFIG_EXPORT) {\n            // export const config = { ... }\n            // Error if using `amp: ...`\n            const value = declaration.initializer\n            if (value && ts.isObjectLiteralExpression(value)) {\n              for (const prop of value.properties) {\n                if (\n                  ts.isPropertyAssignment(prop) &&\n                  ts.isIdentifier(prop.name) &&\n                  prop.name.text === 'amp'\n                ) {\n                  diagnostics.push({\n                    file: source,\n                    category: ts.DiagnosticCategory.Error,\n                    code: NEXT_TS_ERRORS.INVALID_CONFIG_OPTION,\n                    messageText: `AMP is not supported in the app directory. If you need to use AMP it will continue to be supported in the pages directory.`,\n                    start: prop.getStart(),\n                    length: prop.getWidth(),\n                  })\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return diagnostics\n  },\n}\n\nexport default config\n"], "names": ["getSource", "isPositionInsideNode", "getTs", "removeStringQuotes", "NEXT_TS_ERRORS", "ALLOWED_EXPORTS", "LEGACY_CONFIG_EXPORT", "API_DOCS", "dynamic", "description", "options", "link", "fetchCache", "preferredRegion", "<PERSON><PERSON><PERSON><PERSON>", "value", "parsed", "JSON", "parse", "Array", "isArray", "some", "v", "err", "getHint", "join", "revalidate", "type", "false", "Number", "replace", "dynamicParams", "true", "runtime", "metadata", "insertText", "generateMetadata", "maxDuration", "experimental_ppr", "visitEntryConfig", "fileName", "position", "callback", "source", "ts", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isVariableStatement", "modifiers", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "text", "name", "getText", "createAutoCompletionOptionName", "sort", "sortText", "ScriptElementKind", "constElement", "kindModifiers", "ScriptElementKindModifier", "exportedModifier", "labelDetails", "data", "exportName", "moduleSpecifier", "createAutoCompletionOptionValue", "apiName", "isString", "startsWith", "string", "unknown", "none", "getAPIDescription", "api", "Object", "entries", "map", "key", "config", "addCompletionsAtPosition", "prior", "entryConfig", "push", "keys", "index", "getQuickInfoAtPosition", "overridden", "initializer", "docsLink", "isStringLiteral", "enumElement", "textSpan", "start", "getStart", "length", "getWidth", "displayParts", "documentation", "getCompletionEntryDetails", "entryName", "content", "sourceFile", "foundMetadataImport", "visitor", "isExpressionStatement", "expression", "end", "isImportDeclaration", "namedImports", "importClause", "<PERSON><PERSON><PERSON><PERSON>", "isNamedImports", "elements", "element", "propertyName", "statement", "statements", "visitNode", "codeActions", "undefined", "changes", "textChanges", "span", "newText", "getSemanticDiagnosticsForExportVariableStatement", "diagnostics", "isIdentifier", "includes", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_ENTRY_EXPORT", "messageText", "displayedValue", "errorMessage", "isInvalid", "isNoSubstitutionTemplateLiteral", "val", "<PERSON><PERSON><PERSON><PERSON>", "filter", "test", "isNumericLiteral", "isPrefixUnaryExpression", "isMinusToken", "operator", "operand", "TrueKeyword", "FalseKeyword", "isArrayLiteralExpression", "stringify", "e", "isBigIntLiteral", "isObjectLiteralExpression", "isRegularExpressionLiteral", "INVALID_OPTION_VALUE", "prop", "properties", "isPropertyAssignment", "INVALID_CONFIG_OPTION"], "mappings": "AAAA,4EAA4E;AAE5E,SACEA,SAAS,EACTC,oBAAoB,EACpBC,KAAK,EACLC,kBAAkB,QACb,WAAU;AACjB,SACEC,cAAc,EACdC,eAAe,EACfC,oBAAoB,QACf,cAAa;AAGpB,MAAMC,WAWF;IACFC,SAAS;QACPC,aACE;QACFC,SAAS;YACP,UACE;YACF,mBACE;YACF,WACE;YACF,kBACE;QACJ;QACAC,MAAM;IACR;IACAC,YAAY;QACVH,aACE;QACFC,SAAS;YACP,oBACE;YACF,mBACE;YACF,sBACE;YACF,UACE;YACF,mBACE;YACF,gBACE;YACF,iBACE;QACJ;QACAC,MAAM;IACR;IACAE,iBAAiB;QACfJ,aACE;QACFC,SAAS;YACP,UACE;YACF,YAAY;YACZ,UAAU;QACZ;QACAC,MAAM;QACNG,SAAS,CAACC;YACR,IAAI;gBACF,MAAMC,SAASC,KAAKC,KAAK,CAACH;gBAC1B,OACE,OAAOC,WAAW,YACjBG,MAAMC,OAAO,CAACJ,WAAW,CAACA,OAAOK,IAAI,CAAC,CAACC,IAAM,OAAOA,MAAM;YAE/D,EAAE,OAAOC,KAAK;gBACZ,OAAO;YACT;QACF;QACAC,SAAS,CAACT;YACR,IAAIA,UAAU,QAAQ,OAAO,CAAC,gCAAgC,CAAC;YAC/D,IAAIA,UAAU,UAAU,OAAO,CAAC,0BAA0B,CAAC;YAC3D,IAAIA,UAAU,QAAQ,OAAO,CAAC,oCAAoC,CAAC;YACnE,IAAII,MAAMC,OAAO,CAACL,QAAQ,OAAO,CAAC,mBAAmB,EAAEA,MAAMU,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,OAAOV,UAAU,UAAU,OAAO,CAAC,kBAAkB,EAAEA,MAAM,CAAC,CAAC;QACrE;IACF;IACAW,YAAY;QACVjB,aACE;QACFkB,MAAM;QACNjB,SAAS;YACPkB,OACE;YACF,GAAG;YACH,IAAI;QACN;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,WAAWc,OAAOd,MAAMe,OAAO,CAAC,MAAM,QAAQ;QACjE;QACAN,SAAS,CAACT;YACR,OAAO,CAAC,uCAAuC,EAAEA,MAAM,WAAW,CAAC;QACrE;IACF;IACAgB,eAAe;QACbtB,aACE;QACFC,SAAS;YACPsB,MAAM;YACNJ,OACE;QACJ;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,UAAUA,UAAU;QACvC;IACF;IACAkB,SAAS;QACPxB,aACE;QACFC,SAAS;YACP,YAAY;YACZ,UAAU;YACV,uBAAuB,CAAC,2EAA2E,CAAC;QACtG;QACAC,MAAM;IACR;IACAuB,UAAU;QACRzB,aAAa;QACbE,MAAM;QACNwB,YAAY;IACd;IACAC,kBAAkB;QAChB3B,aAAa;QACbE,MAAM;QACNwB,YAAY;IACd;IACAE,aAAa;QACX5B,aACE;QACFE,MAAM;IACR;IACA2B,kBAAkB;QAChB7B,aAAa,CAAC,8GAA8G,CAAC;QAC7HE,MAAM;QACND,SAAS;YACPsB,MAAM;YACNJ,OAAO;QACT;QACAd,SAAS,CAACC;YACR,OAAOA,UAAU,UAAUA,UAAU;QACvC;IACF;AACF;AAEA,SAASwB,iBACPC,QAAgB,EAChBC,QAAgB,EAChBC,QAA4E;IAE5E,MAAMC,SAAS3C,UAAUwC;IACzB,IAAIG,QAAQ;QACV,MAAMC,KAAK1C;QACX0C,GAAGC,YAAY,CAACF,QAAQ,SAASG,MAAMC,IAAI;YACzC,uBAAuB;YACvB,IAAI9C,qBAAqBwC,UAAUM,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEH,GAAGI,mBAAmB,CAACD,WACvBA,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgB1B,IAAI,CAAC,CAAC6B,IAAMA,EAAEC,IAAI,KAAKP,GAAGQ,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIT,GAAGU,yBAAyB,CAACP,KAAKQ,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeT,KAAKQ,eAAe,CAACE,YAAY,CAAE;4BAC3D,IAAIxD,qBAAqBwC,UAAUe,cAAc;gCAC/C,2BAA2B;gCAC3B,MAAME,OAAOF,YAAYG,IAAI,CAACC,OAAO;gCACrClB,SAASgB,MAAMF;4BACjB;wBACF;oBACF;gBACF;YACF;QACF;IACF;AACF;AAEA,SAASK,+BAA+BC,IAAY,EAAEH,IAAY;IAChE,MAAMf,KAAK1C;IAEX,OAAO;QACLyD;QACAxB,YAAY5B,QAAQ,CAACoD,KAAK,CAACxB,UAAU;QACrC4B,UAAU,MAAMD;QAChBX,MAAMP,GAAGoB,iBAAiB,CAACC,YAAY;QACvCC,eAAetB,GAAGuB,yBAAyB,CAACC,gBAAgB;QAC5DC,cAAc;YACZ5D,aAAa,CAAC,QAAQ,EAAEkD,KAAK,OAAO,CAAC;QACvC;QACAW,MAAM;YACJC,YAAYZ;YACZa,iBAAiB;QACnB;IACF;AACF;AAEA,SAASC,gCACPX,IAAY,EACZH,IAAY,EACZe,OAAe;IAEf,MAAM9B,KAAK1C;IACX,MAAMyE,WAAWhB,KAAKiB,UAAU,CAAC;IACjC,OAAO;QACLjB;QACAxB,YAAYhC,mBAAmBwD;QAC/BI,UAAU,KAAKD;QACfX,MAAMwB,WAAW/B,GAAGoB,iBAAiB,CAACa,MAAM,GAAGjC,GAAGoB,iBAAiB,CAACc,OAAO;QAC3EZ,eAAetB,GAAGuB,yBAAyB,CAACY,IAAI;QAChDV,cAAc;YACZ5D,aAAa,CAAC,QAAQ,EAAEiE,QAAQ,OAAO,CAAC;QAC1C;QACAJ,MAAM;YACJC,YAAYG;YACZF,iBAAiB;QACnB;IACF;AACF;AAEA,SAASQ,kBAAkBC,GAAW;IACpC,OACE1E,QAAQ,CAAC0E,IAAI,CAACxE,WAAW,GACzB,SACAyE,OAAOC,OAAO,CAAC5E,QAAQ,CAAC0E,IAAI,CAACvE,OAAO,IAAI,CAAC,GACtC0E,GAAG,CAAC,CAAC,CAACC,KAAKtE,MAAM,GAAK,CAAC,IAAI,EAAEsE,IAAI,IAAI,EAAEtE,OAAO,EAC9CU,IAAI,CAAC;AAEZ;AAEA,MAAM6D,SAAS;IACb,8CAA8C;IAC9CC,0BACE/C,QAAgB,EAChBC,QAAgB,EAChB+C,KAAqD;QAErDjD,iBAAiBC,UAAUC,UAAU,CAACgD,aAAajC;YACjD,IAAI,CAACjD,QAAQ,CAACkF,YAAY,EAAE;gBAC1B,IAAIxF,qBAAqBwC,UAAUe,YAAYG,IAAI,GAAG;oBACpD6B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAACpF,UAAU6E,GAAG,CAAC,CAACzB,MAAMiC;wBAClC,OAAO/B,+BAA+B+B,OAAOjC;oBAC/C;gBAEJ;gBACA;YACF;YAEA6B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAACpF,QAAQ,CAACkF,YAAY,CAAC/E,OAAO,IAAI,CAAC,GAAG0E,GAAG,CACrD,CAACzB,MAAMiC;gBACL,OAAOnB,gCAAgCmB,OAAOjC,MAAM8B;YACtD;QAGN;IACF;IAEA,mDAAmD;IACnDI,wBAAuBrD,QAAgB,EAAEC,QAAgB;QACvD,MAAMG,KAAK1C;QAEX,IAAI4F;QACJvD,iBAAiBC,UAAUC,UAAU,CAACgD,aAAajC;YACjD,IAAI,CAACjD,QAAQ,CAACkF,YAAY,EAAE;YAE5B,MAAM9B,OAAOH,YAAYG,IAAI;YAC7B,MAAM5C,QAAQyC,YAAYuC,WAAW;YAErC,MAAMC,WAAW;gBACf7C,MAAM;gBACNO,MACE,CAAC,yBAAyB,EAAE+B,YAAY,UAAU,CAAC,GACnDlF,QAAQ,CAACkF,YAAY,CAAC9E,IAAI;YAC9B;YAEA,IAAII,SAASd,qBAAqBwC,UAAU1B,QAAQ;oBAO9CR,+BAAAA,uBACEA;gBAPN,iCAAiC;gBACjC,MAAMoE,WAAW/B,GAAGqD,eAAe,CAAClF;gBACpC,MAAM2C,OAAOvD,mBAAmBY,MAAM6C,OAAO;gBAC7C,MAAMyB,MAAMV,WAAW,CAAC,CAAC,EAAEjB,KAAK,CAAC,CAAC,GAAGA;gBAErC,MAAM5C,UAAUP,QAAQ,CAACkF,YAAY,CAAC3E,OAAO,IACzCP,gCAAAA,CAAAA,wBAAAA,QAAQ,CAACkF,YAAY,EAAC3E,OAAO,qBAA7BP,mCAAAA,uBAAgC8E,OAChC,CAAC,GAAC9E,gCAAAA,QAAQ,CAACkF,YAAY,CAAC/E,OAAO,qBAA7BH,6BAA+B,CAAC8E,IAAI;gBAE1C,IAAIvE,SAAS;wBAaHP,gCACAA,+BAAAA;oBAbRuF,aAAa;wBACX3C,MAAMP,GAAGoB,iBAAiB,CAACkC,WAAW;wBACtChC,eAAetB,GAAGuB,yBAAyB,CAACY,IAAI;wBAChDoB,UAAU;4BACRC,OAAOrF,MAAMsF,QAAQ;4BACrBC,QAAQvF,MAAMwF,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BACb;gCACEtD,MAAM;gCACNO,MACEnD,EAAAA,iCAAAA,QAAQ,CAACkF,YAAY,CAAC/E,OAAO,qBAA7BH,8BAA+B,CAAC8E,IAAI,OACpC9E,gCAAAA,CAAAA,yBAAAA,QAAQ,CAACkF,YAAY,EAACjE,OAAO,qBAA7BjB,mCAAAA,wBAAgC8E,SAChC;4BACJ;4BACAW;yBACD;oBACH;gBACF,OAAO;oBACL,qCAAqC;oBACrCF,aAAa;wBACX3C,MAAMP,GAAGoB,iBAAiB,CAACkC,WAAW;wBACtChC,eAAetB,GAAGuB,yBAAyB,CAACY,IAAI;wBAChDoB,UAAU;4BACRC,OAAOrF,MAAMsF,QAAQ;4BACrBC,QAAQvF,MAAMwF,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BAACT;yBAAS;oBAC3B;gBACF;YACF,OAAO;gBACL,gCAAgC;gBAChCF,aAAa;oBACX3C,MAAMP,GAAGoB,iBAAiB,CAACkC,WAAW;oBACtChC,eAAetB,GAAGuB,yBAAyB,CAACY,IAAI;oBAChDoB,UAAU;wBACRC,OAAOzC,KAAK0C,QAAQ;wBACpBC,QAAQ3C,KAAK4C,QAAQ;oBACvB;oBACAC,cAAc,EAAE;oBAChBC,eAAe;wBACb;4BACEtD,MAAM;4BACNO,MAAMsB,kBAAkBS;wBAC1B;wBACAO;qBACD;gBACH;YACF;QACF;QACA,OAAOF;IACT;IAEA,iDAAiD;IACjDY,2BACEC,SAAiB,EACjBrC,IAAkC,EAClC9B,QAAgB;QAEhB,MAAMI,KAAK1C;QACX,IACEoE,QACAA,KAAKE,eAAe,IACpBF,KAAKE,eAAe,CAACI,UAAU,CAAC,oBAChC;YACA,IAAIgC,UAAU;YACd,IAAItC,KAAKE,eAAe,KAAK,qCAAqC;gBAChEoC,UAAU5B,kBAAkB2B;YAC9B,OAAO;gBACL,MAAMjG,UAAUH,QAAQ,CAAC+D,KAAKC,UAAU,CAAC,CAAC7D,OAAO;gBACjD,IAAI,CAACA,SAAS;gBACdkG,UAAUlG,OAAO,CAACiG,UAAU;YAC9B;YAEA,IAAIA,cAAc,cAAcA,cAAc,oBAAoB;gBAChE,MAAME,aAAa7G,UAAUwC;gBAC7B,IAAI4D,QAAQ;gBACZ,IAAIU,sBAAsB;gBAE1B,IAAID,YAAY;oBACd,MAAME,UAA4B,CAAChE;wBACjC,0BAA0B;wBAC1B,IACEH,GAAGoE,qBAAqB,CAACjE,SACzBH,GAAGqD,eAAe,CAAClD,KAAKkE,UAAU,KAClClE,KAAKkE,UAAU,CAACZ,QAAQ,OAAO,GAC/B;4BACA,MAAM3C,OAAOX,KAAKkE,UAAU,CAACvD,IAAI;4BACjC,IAAIA,KAAKkB,UAAU,CAAC,SAAS;gCAC3BwB,QAAQrD,KAAKmE,GAAG,GAAG;gCACnB,OAAOnE,KAAK,qBAAqB;;4BACnC;wBACF;wBAEA,4BAA4B;wBAC5B,IACEH,GAAGuE,mBAAmB,CAACpE,SACtBA,CAAAA,KAAKyB,eAAe,CAACZ,OAAO,OAAO,YAClCb,KAAKyB,eAAe,CAACZ,OAAO,OAAO,QAAO,GAC5C;gCACqBb;4BAArB,MAAMqE,gBAAerE,qBAAAA,KAAKsE,YAAY,qBAAjBtE,mBAAmBuE,aAAa;4BACrD,IAAIF,gBAAgBxE,GAAG2E,cAAc,CAACH,eAAe;gCACnDN,sBAAsBM,aAAaI,QAAQ,CAACnG,IAAI,CAAC,CAACoG;wCAE3BA;oCADrB,MAAM9D,OAAO8D,QAAQ9D,IAAI,CAACC,OAAO;oCACjC,MAAM8D,gBAAeD,wBAAAA,QAAQC,YAAY,qBAApBD,sBAAsB7D,OAAO;oCAClD,OAAOD,SAAS,cAAc+D,iBAAiB;gCACjD;gCACA,IAAIZ,qBAAqB;oCACvB,QAAO,iBAAiB;gCAC1B;4BACF;wBACF;wBAEA,OAAO/D;oBACT;oBAEA,KAAK,MAAM4E,aAAad,WAAWe,UAAU,CAAE;wBAC7C,IAAId,qBAAqB;wBACzBlE,GAAGiF,SAAS,CAACF,WAAWZ;oBAC1B;gBACF;gBAEA,OAAO;oBACLpD,MAAMgD;oBACNxD,MAAMP,GAAGoB,iBAAiB,CAACkC,WAAW;oBACtChC,eAAetB,GAAGuB,yBAAyB,CAACY,IAAI;oBAChDyB,cAAc,EAAE;oBAChBsB,aAAahB,sBACTiB,YACA;wBACE;4BACEtH,aAAa,CAAC,yCAAyC,CAAC;4BACxDuH,SAAS;gCACP;oCACExF;oCACAyF,aAAa;wCACX;4CACEC,MAAM;gDAAE9B;gDAAOE,QAAQ;4CAAE;4CACzB6B,SAAS,CAAC,uCAAuC,CAAC;wCACpD;qCACD;gCACH;6BACD;wBACH;qBACD;oBACL1B,eAAe;wBACb;4BACEtD,MAAM;4BACNO,MAAMkD;wBACR;qBACD;gBACH;YACF;YAEA,OAAO;gBACLjD,MAAMgD;gBACNxD,MAAMP,GAAGoB,iBAAiB,CAACkC,WAAW;gBACtChC,eAAetB,GAAGuB,yBAAyB,CAACY,IAAI;gBAChDyB,cAAc,EAAE;gBAChBC,eAAe;oBACb;wBACEtD,MAAM;wBACNO,MAAMkD;oBACR;iBACD;YACH;QACF;IACF;IAEA,yCAAyC;IACzCwB,kDACEzF,MAA2B,EAC3BI,IAAgC;QAEhC,MAAMH,KAAK1C;QAEX,MAAMmI,cAAqC,EAAE;QAE7C,yCAAyC;QACzC,IAAIzF,GAAGU,yBAAyB,CAACP,KAAKQ,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeT,KAAKQ,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAME,OAAOH,YAAYG,IAAI;gBAC7B,IAAIf,GAAG0F,YAAY,CAAC3E,OAAO;oBACzB,IAAI,CAACtD,gBAAgBkI,QAAQ,CAAC5E,KAAKD,IAAI,KAAK,CAACnD,QAAQ,CAACoD,KAAKD,IAAI,CAAC,EAAE;wBAChE2E,YAAY3C,IAAI,CAAC;4BACf8C,MAAM7F;4BACN8F,UAAU7F,GAAG8F,kBAAkB,CAACC,KAAK;4BACrCC,MAAMxI,eAAeyI,oBAAoB;4BACzCC,aAAa,CAAC,CAAC,EAAEnF,KAAKD,IAAI,CAAC,4CAA4C,CAAC;4BACxE0C,OAAOzC,KAAK0C,QAAQ;4BACpBC,QAAQ3C,KAAK4C,QAAQ;wBACvB;oBACF,OAAO,IAAIhG,QAAQ,CAACoD,KAAKD,IAAI,CAAC,EAAE;wBAC9B,8BAA8B;wBAC9B,MAAM3C,QAAQyC,YAAYuC,WAAW;wBACrC,MAAMrF,UAAUH,QAAQ,CAACoD,KAAKD,IAAI,CAAC,CAAChD,OAAO;wBAE3C,IAAIK,SAASL,SAAS;4BACpB,IAAIqI,iBAAiB;4BACrB,IAAIC,eAAe;4BACnB,IAAIC,YAAY;4BAEhB,IACErG,GAAGqD,eAAe,CAAClF,UACnB6B,GAAGsG,+BAA+B,CAACnI,QACnC;oCAQGR,6BAAAA;gCAPH,MAAM4I,MAAM,MAAMhJ,mBAAmBY,MAAM6C,OAAO,MAAM;gCACxD,MAAMwF,gBAAgBlE,OAAOS,IAAI,CAACjF,SAAS2I,MAAM,CAAC,CAAC/H,IACjD,QAAQgI,IAAI,CAAChI;gCAGf,IACE,CAAC8H,cAAcb,QAAQ,CAACY,QACxB,GAAC5I,8BAAAA,CAAAA,sBAAAA,QAAQ,CAACoD,KAAKD,IAAI,CAAC,EAAC5C,OAAO,qBAA3BP,iCAAAA,qBAA8B4I,OAC/B;oCACAF,YAAY;oCACZF,iBAAiBI;gCACnB;4BACF,OAAO,IACLvG,GAAG2G,gBAAgB,CAACxI,UACnB6B,GAAG4G,uBAAuB,CAACzI,UAC1B6B,GAAG6G,YAAY,CAAC,AAAC1I,MAAc2I,QAAQ,KACtC9G,CAAAA,GAAG2G,gBAAgB,CAAC,AAACxI,MAAc4I,OAAO,CAACxG,IAAI,KAC7CP,GAAG0F,YAAY,CAAC,AAACvH,MAAc4I,OAAO,CAACxG,IAAI,KAC1C,AAACpC,MAAc4I,OAAO,CAACxG,IAAI,CAACS,OAAO,OAAO,UAAU,KACzDhB,GAAG0F,YAAY,CAACvH,UAAUA,MAAM6C,OAAO,OAAO,YAC/C;oCAEKrD,8BAAAA;gCADL,MAAMe,IAAIP,MAAM6C,OAAO;gCACvB,IAAI,GAACrD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACoD,KAAKD,IAAI,CAAC,EAAC5C,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrC2H,YAAY;oCACZF,iBAAiBzH;gCACnB;4BACF,OAAO,IACLP,MAAMoC,IAAI,KAAKP,GAAGQ,UAAU,CAACwG,WAAW,IACxC7I,MAAMoC,IAAI,KAAKP,GAAGQ,UAAU,CAACyG,YAAY,EACzC;oCAEKtJ,8BAAAA;gCADL,MAAMe,IAAIP,MAAM6C,OAAO;gCACvB,IAAI,GAACrD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACoD,KAAKD,IAAI,CAAC,EAAC5C,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrC2H,YAAY;oCACZF,iBAAiBzH;gCACnB;4BACF,OAAO,IAAIsB,GAAGkH,wBAAwB,CAAC/I,QAAQ;oCAG1CR,8BAAAA;gCAFH,MAAMe,IAAIP,MAAM6C,OAAO;gCACvB,IACE,GAACrD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACoD,KAAKD,IAAI,CAAC,EAAC5C,OAAO,qBAA3BP,kCAAAA,sBACCU,KAAK8I,SAAS,CAAChJ,MAAMyG,QAAQ,CAACpC,GAAG,CAAC,CAAC4E,IAAMA,EAAEpG,OAAO,QAEpD;oCACAqF,YAAY;oCACZF,iBAAiBzH;gCACnB;4BACF,OAAO,IACL,iBAAiB;4BACjBsB,GAAGqH,eAAe,CAAClJ,UACnB6B,GAAGsH,yBAAyB,CAACnJ,UAC7B6B,GAAGuH,0BAA0B,CAACpJ,UAC9B6B,GAAG4G,uBAAuB,CAACzI,QAC3B;gCACAkI,YAAY;gCACZF,iBAAiBhI,MAAM6C,OAAO;4BAChC,OAAO;gCACL,8DAA8D;gCAC9DqF,YAAY;gCACZF,iBAAiBhI,MAAM6C,OAAO;gCAC9BoF,eAAe,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAEpF,KAAKD,IAAI,CAAC,0DAA0D,CAAC;4BAC3I;4BAEA,IAAIuF,WAAW;gCACbZ,YAAY3C,IAAI,CAAC;oCACf8C,MAAM7F;oCACN8F,UAAU7F,GAAG8F,kBAAkB,CAACC,KAAK;oCACrCC,MAAMxI,eAAegK,oBAAoB;oCACzCtB,aACEE,gBACA,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAEpF,KAAKD,IAAI,CAAC,SAAS,CAAC;oCAC3E0C,OAAOrF,MAAMsF,QAAQ;oCACrBC,QAAQvF,MAAMwF,QAAQ;gCACxB;4BACF;wBACF;oBACF,OAAO,IAAI5C,KAAKD,IAAI,KAAKpD,sBAAsB;wBAC7C,gCAAgC;wBAChC,4BAA4B;wBAC5B,MAAMS,QAAQyC,YAAYuC,WAAW;wBACrC,IAAIhF,SAAS6B,GAAGsH,yBAAyB,CAACnJ,QAAQ;4BAChD,KAAK,MAAMsJ,QAAQtJ,MAAMuJ,UAAU,CAAE;gCACnC,IACE1H,GAAG2H,oBAAoB,CAACF,SACxBzH,GAAG0F,YAAY,CAAC+B,KAAK1G,IAAI,KACzB0G,KAAK1G,IAAI,CAACD,IAAI,KAAK,OACnB;oCACA2E,YAAY3C,IAAI,CAAC;wCACf8C,MAAM7F;wCACN8F,UAAU7F,GAAG8F,kBAAkB,CAACC,KAAK;wCACrCC,MAAMxI,eAAeoK,qBAAqB;wCAC1C1B,aAAa,CAAC,0HAA0H,CAAC;wCACzI1C,OAAOiE,KAAKhE,QAAQ;wCACpBC,QAAQ+D,KAAK9D,QAAQ;oCACvB;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO8B;IACT;AACF;AAEA,eAAe/C,OAAM", "ignoreList": [0]}