{"version": 3, "sources": ["../../../src/server/route-matcher-providers/pages-api-route-matcher-provider.ts"], "sourcesContent": ["import { isAPIRoute } from '../../lib/is-api-route'\nimport { PAGES_MANIFEST } from '../../shared/lib/constants'\nimport { RouteKind } from '../route-kind'\nimport {\n  PagesAPILocaleRouteMatcher,\n  PagesAPIRouteMatcher,\n} from '../route-matchers/pages-api-route-matcher'\nimport type {\n  Manife<PERSON>,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { ManifestRouteMatcherProvider } from './manifest-route-matcher-provider'\nimport type { I18NProvider } from '../lib/i18n-provider'\nimport { PagesNormalizers } from '../normalizers/built/pages'\n\nexport class PagesAPIRouteMatcherProvider extends ManifestRouteMatcherProvider<PagesAPIRouteMatcher> {\n  private readonly normalizers: PagesNormalizers\n\n  constructor(\n    distDir: string,\n    manifestLoader: ManifestLoader,\n    private readonly i18nProvider?: I18NProvider\n  ) {\n    super(PAGES_MANIFEST, manifestLoader)\n\n    this.normalizers = new PagesNormalizers(distDir)\n  }\n\n  protected async transform(\n    manifest: Manifest\n  ): Promise<ReadonlyArray<PagesAPIRouteMatcher>> {\n    // This matcher is only for Pages API routes.\n    const pathnames = Object.keys(manifest).filter((pathname) =>\n      isAPIRoute(pathname)\n    )\n\n    const matchers: Array<PagesAPIRouteMatcher> = []\n\n    for (const page of pathnames) {\n      if (this.i18nProvider) {\n        // Match the locale on the page name, or default to the default locale.\n        const { detectedLocale, pathname } = this.i18nProvider.analyze(page)\n\n        matchers.push(\n          new PagesAPILocaleRouteMatcher({\n            kind: RouteKind.PAGES_API,\n            pathname,\n            page,\n            bundlePath: this.normalizers.bundlePath.normalize(page),\n            filename: this.normalizers.filename.normalize(manifest[page]),\n            i18n: {\n              locale: detectedLocale,\n            },\n          })\n        )\n      } else {\n        matchers.push(\n          new PagesAPIRouteMatcher({\n            kind: RouteKind.PAGES_API,\n            // In `pages/`, the page is the same as the pathname.\n            pathname: page,\n            page,\n            bundlePath: this.normalizers.bundlePath.normalize(page),\n            filename: this.normalizers.filename.normalize(manifest[page]),\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["isAPIRoute", "PAGES_MANIFEST", "RouteKind", "PagesAPILocaleRouteMatcher", "PagesAPIRouteMatcher", "ManifestRouteMatcherProvider", "PagesNormalizers", "PagesAPIRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "i18nProvider", "normalizers", "transform", "manifest", "pathnames", "Object", "keys", "filter", "pathname", "matchers", "page", "detectedLocale", "analyze", "push", "kind", "PAGES_API", "bundlePath", "normalize", "filename", "i18n", "locale"], "mappings": "AAAA,SAASA,UAAU,QAAQ,yBAAwB;AACnD,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,SAAS,QAAQ,gBAAe;AACzC,SACEC,0BAA0B,EAC1BC,oBAAoB,QACf,4CAA2C;AAKlD,SAASC,4BAA4B,QAAQ,oCAAmC;AAEhF,SAASC,gBAAgB,QAAQ,6BAA4B;AAE7D,OAAO,MAAMC,qCAAqCF;IAGhDG,YACEC,OAAe,EACfC,cAA8B,EAC9B,AAAiBC,YAA2B,CAC5C;QACA,KAAK,CAACV,gBAAgBS,sBAFLC,eAAAA;QAIjB,IAAI,CAACC,WAAW,GAAG,IAAIN,iBAAiBG;IAC1C;IAEA,MAAgBI,UACdC,QAAkB,EAC4B;QAC9C,6CAA6C;QAC7C,MAAMC,YAAYC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,WAC9CnB,WAAWmB;QAGb,MAAMC,WAAwC,EAAE;QAEhD,KAAK,MAAMC,QAAQN,UAAW;YAC5B,IAAI,IAAI,CAACJ,YAAY,EAAE;gBACrB,uEAAuE;gBACvE,MAAM,EAAEW,cAAc,EAAEH,QAAQ,EAAE,GAAG,IAAI,CAACR,YAAY,CAACY,OAAO,CAACF;gBAE/DD,SAASI,IAAI,CACX,IAAIrB,2BAA2B;oBAC7BsB,MAAMvB,UAAUwB,SAAS;oBACzBP;oBACAE;oBACAM,YAAY,IAAI,CAACf,WAAW,CAACe,UAAU,CAACC,SAAS,CAACP;oBAClDQ,UAAU,IAAI,CAACjB,WAAW,CAACiB,QAAQ,CAACD,SAAS,CAACd,QAAQ,CAACO,KAAK;oBAC5DS,MAAM;wBACJC,QAAQT;oBACV;gBACF;YAEJ,OAAO;gBACLF,SAASI,IAAI,CACX,IAAIpB,qBAAqB;oBACvBqB,MAAMvB,UAAUwB,SAAS;oBACzB,qDAAqD;oBACrDP,UAAUE;oBACVA;oBACAM,YAAY,IAAI,CAACf,WAAW,CAACe,UAAU,CAACC,SAAS,CAACP;oBAClDQ,UAAU,IAAI,CAACjB,WAAW,CAACiB,QAAQ,CAACD,SAAS,CAACd,QAAQ,CAACO,KAAK;gBAC9D;YAEJ;QACF;QAEA,OAAOD;IACT;AACF", "ignoreList": [0]}