{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/dev/dev-app-page-route-matcher-provider.ts"], "sourcesContent": ["import type { <PERSON><PERSON>ead<PERSON> } from './helpers/file-reader/file-reader'\nimport { AppPageRouteMatcher } from '../../route-matchers/app-page-route-matcher'\nimport { RouteKind } from '../../route-kind'\nimport { FileCacheRouteMatcherProvider } from './file-cache-route-matcher-provider'\n\nimport { DevAppNormalizers } from '../../normalizers/built/app'\nimport { normalizeCatchAllRoutes } from '../../../build/normalize-catchall-routes'\n\nexport class DevAppPageRouteMatcherProvider extends FileCacheRouteMatcherProvider<AppPageRouteMatcher> {\n  private readonly expression: RegExp\n  private readonly normalizers: DevAppNormalizers\n  private readonly isTurbopack: boolean\n\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    reader: FileReader,\n    isTurbopack: boolean\n  ) {\n    super(appDir, reader)\n\n    this.normalizers = new DevAppNormalizers(appDir, extensions, isTurbopack)\n\n    // Match any page file that ends with `/page.${extension}` or `/default.${extension}` under the app\n    // directory.\n    this.expression = new RegExp(\n      `[/\\\\\\\\](page|default)\\\\.(?:${extensions.join('|')})$`\n    )\n    this.isTurbopack = isTurbopack\n  }\n\n  protected async transform(\n    files: ReadonlyArray<string>\n  ): Promise<ReadonlyArray<AppPageRouteMatcher>> {\n    // Collect all the app paths for each page. This could include any parallel\n    // routes.\n    const cache = new Map<\n      string,\n      { page: string; pathname: string; bundlePath: string }\n    >()\n    const routeFilenames = new Array<string>()\n    let appPaths: Record<string, string[]> = {}\n    for (const filename of files) {\n      // If the file isn't a match for this matcher, then skip it.\n      if (!this.expression.test(filename)) continue\n\n      let page = this.normalizers.page.normalize(filename)\n\n      // Validate that this is not an ignored page.\n      if (page.includes('/_')) continue\n\n      // Turbopack uses the correct page name with the underscore normalized.\n      // TODO: Move implementation to packages/next/src/server/normalizers/built/app/app-page-normalizer.ts.\n      // The `includes('/_')` check above needs to be moved for that to work as otherwise `%5Fsegmentname`\n      // will result in `_segmentname` which hits that includes check and be skipped.\n      if (this.isTurbopack) {\n        page = page.replace(/%5F/g, '_')\n      }\n\n      // This is a valid file that we want to create a matcher for.\n      routeFilenames.push(filename)\n\n      const pathname = this.normalizers.pathname.normalize(filename)\n      const bundlePath = this.normalizers.bundlePath.normalize(filename)\n\n      // Save the normalization results.\n      cache.set(filename, { page, pathname, bundlePath })\n\n      if (pathname in appPaths) appPaths[pathname].push(page)\n      else appPaths[pathname] = [page]\n    }\n\n    normalizeCatchAllRoutes(appPaths)\n\n    // Make sure to sort parallel routes to make the result deterministic.\n    appPaths = Object.fromEntries(\n      Object.entries(appPaths).map(([k, v]) => [k, v.sort()])\n    )\n\n    const matchers: Array<AppPageRouteMatcher> = []\n    for (const filename of routeFilenames) {\n      // Grab the cached values (and the appPaths).\n      const cached = cache.get(filename)\n      if (!cached) {\n        throw new Error('Invariant: expected filename to exist in cache')\n      }\n      const { pathname, page, bundlePath } = cached\n\n      matchers.push(\n        new AppPageRouteMatcher({\n          kind: RouteKind.APP_PAGE,\n          pathname,\n          page,\n          bundlePath,\n          filename,\n          appPaths: appPaths[pathname],\n        })\n      )\n    }\n    return matchers\n  }\n}\n"], "names": ["AppPageRouteMatcher", "RouteKind", "FileCacheRouteMatcherProvider", "DevAppNormalizers", "normalizeCatchAllRoutes", "DevAppPageRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "isTurbopack", "normalizers", "expression", "RegExp", "join", "transform", "files", "cache", "Map", "routeFilenames", "Array", "appPaths", "filename", "test", "page", "normalize", "includes", "replace", "push", "pathname", "bundlePath", "set", "Object", "fromEntries", "entries", "map", "k", "v", "sort", "matchers", "cached", "get", "Error", "kind", "APP_PAGE"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,8CAA6C;AACjF,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SAASC,6BAA6B,QAAQ,sCAAqC;AAEnF,SAASC,iBAAiB,QAAQ,8BAA6B;AAC/D,SAASC,uBAAuB,QAAQ,2CAA0C;AAElF,OAAO,MAAMC,uCAAuCH;IAKlDI,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,EAClBC,WAAoB,CACpB;QACA,KAAK,CAACH,QAAQE;QAEd,IAAI,CAACE,WAAW,GAAG,IAAIR,kBAAkBI,QAAQC,YAAYE;QAE7D,mGAAmG;QACnG,aAAa;QACb,IAAI,CAACE,UAAU,GAAG,IAAIC,OACpB,CAAC,2BAA2B,EAAEL,WAAWM,IAAI,CAAC,KAAK,EAAE,CAAC;QAExD,IAAI,CAACJ,WAAW,GAAGA;IACrB;IAEA,MAAgBK,UACdC,KAA4B,EACiB;QAC7C,2EAA2E;QAC3E,UAAU;QACV,MAAMC,QAAQ,IAAIC;QAIlB,MAAMC,iBAAiB,IAAIC;QAC3B,IAAIC,WAAqC,CAAC;QAC1C,KAAK,MAAMC,YAAYN,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACJ,UAAU,CAACW,IAAI,CAACD,WAAW;YAErC,IAAIE,OAAO,IAAI,CAACb,WAAW,CAACa,IAAI,CAACC,SAAS,CAACH;YAE3C,6CAA6C;YAC7C,IAAIE,KAAKE,QAAQ,CAAC,OAAO;YAEzB,uEAAuE;YACvE,sGAAsG;YACtG,oGAAoG;YACpG,+EAA+E;YAC/E,IAAI,IAAI,CAAChB,WAAW,EAAE;gBACpBc,OAAOA,KAAKG,OAAO,CAAC,QAAQ;YAC9B;YAEA,6DAA6D;YAC7DR,eAAeS,IAAI,CAACN;YAEpB,MAAMO,WAAW,IAAI,CAAClB,WAAW,CAACkB,QAAQ,CAACJ,SAAS,CAACH;YACrD,MAAMQ,aAAa,IAAI,CAACnB,WAAW,CAACmB,UAAU,CAACL,SAAS,CAACH;YAEzD,kCAAkC;YAClCL,MAAMc,GAAG,CAACT,UAAU;gBAAEE;gBAAMK;gBAAUC;YAAW;YAEjD,IAAID,YAAYR,UAAUA,QAAQ,CAACQ,SAAS,CAACD,IAAI,CAACJ;iBAC7CH,QAAQ,CAACQ,SAAS,GAAG;gBAACL;aAAK;QAClC;QAEApB,wBAAwBiB;QAExB,sEAAsE;QACtEA,WAAWW,OAAOC,WAAW,CAC3BD,OAAOE,OAAO,CAACb,UAAUc,GAAG,CAAC,CAAC,CAACC,GAAGC,EAAE,GAAK;gBAACD;gBAAGC,EAAEC,IAAI;aAAG;QAGxD,MAAMC,WAAuC,EAAE;QAC/C,KAAK,MAAMjB,YAAYH,eAAgB;YACrC,6CAA6C;YAC7C,MAAMqB,SAASvB,MAAMwB,GAAG,CAACnB;YACzB,IAAI,CAACkB,QAAQ;gBACX,MAAM,qBAA2D,CAA3D,IAAIE,MAAM,mDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0D;YAClE;YACA,MAAM,EAAEb,QAAQ,EAAEL,IAAI,EAAEM,UAAU,EAAE,GAAGU;YAEvCD,SAASX,IAAI,CACX,IAAI5B,oBAAoB;gBACtB2C,MAAM1C,UAAU2C,QAAQ;gBACxBf;gBACAL;gBACAM;gBACAR;gBACAD,UAAUA,QAAQ,CAACQ,SAAS;YAC9B;QAEJ;QACA,OAAOU;IACT;AACF", "ignoreList": [0]}